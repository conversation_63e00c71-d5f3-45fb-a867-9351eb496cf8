You have saved me hours and hours of time. This is a game changer for me
https://x.com/ajruecker/status/1723225423625310485?s=46

I tried Grimoire, and it is fantastic, man, I never wrote a code in my life and with him I can create a website from scratch !!!!!
Wow, I cannot believe what I saw !
https://x.com/God____Hades/status/1722921203449377234?s=20

I just used grimoire gpt to make a web app hosted with replit and ive never coded anything in my entire life
All the code worked 99% of the time
https://x.com/callmedreamboat/status/1730468038355320978?s=46

Grimoire v1.16 is so GOATED! ... I've been trying to solve a problem for a week ... After 40 min with G<PERSON><PERSON>, the backend for the MVP is done! 
https://x.com/PerceivingAI/status/1731765750593892475?s=20

I've tried lots of ai powered coding tools (prompts gpt, autogen, cursor, etc). I gotta say, this is the best I've seen so for initial prototype. I used it for enabling calendar sync in my app, and it's crazy how for it takes my first version
https://x.com/dan_at_fretbin/status/1722708594213503181?s=20

As a new coder, this is the best coding assistant I have used. I pay a few currently. The explanation command is magical.
https://x.com/petrolsexual29/status/1722775358938775759?s=20

I was skeptical vs regular gpt4 but the system prompting you’ve done, hot keys and approach to iterating on the idea all work really nicely together. Definitely speeding me up. Thank you!
https://x.com/jworthy/status/1723015865317155282?s=20

Experimented with your autocoder on whimsical tasks, and I'm astounded by its exceptional performance. Usually, I brace for bugs and multiple iterations, but your code worked flawlessly on the first try. It’s intriguing why Code Interpreter couldn't achieve this.
https://x.com/edmundkudzayi/status/1723391061094002921?s=46

My son is now using Grimoire to help with Lua scripts for Roblox Builder, and he says it’s WAY better at this than base ChatGPT.
https://x.com/sams_antics/status/1723041166680015140?s=46

The excitement is real! I've just created an amazing web/app thanks to the help of @NickADobos and #Grimoire. 🙌💻 An incredible development experience. Thank you, Nick, for making it possible! ✨
https://x.com/sh_street/status/1724205027609186472?s=46

No need for x-mas gifts. All I need is Grimoire.. Jeeez dude.. you just blew my mind!!! :D
https://x.com/P0pcap/status/1724561833468530783?s=20

I’m a ‘noob’ coder & i’ve been using it to build an app that I’ve attempted to build by using standard GPT4 for the past 3 months or so. Still got a distance to go, but the amount I’ve been able to build on the first try with Grimoire is remarkable. A brilliant GPT, thanks Nick.
https://x.com/mitchellgcc95/status/1725206909123510419?s=20

Digging into Grimoire's clever design has been inspiring! As a programmer its led me down some stray paths. And admittedly, I opted for Google when in need of more direct info retrieval. Yet, it's undeniable how much it speeds up the development process.
https://x.com/alangnative/status/1725215712409538705?s=20

@geekyumeko and I spent almost 2 hours trying to push ShipFast repo to 
@github and kept getting this error:
"file exceeds GitHub's size limit"
So, I found this amazing Custom GPT: Grimoire.
And it fixed it for me in 2 mins. 🥹
And told me what the issue was 😅
https://x.com/CSMikeCardona/status/1727081167818797223?s=20

no lie you weren't kidding with the 100x engineer bit. easily outperforms code-interpreter and also inline support on cursor.
https://x.com/agrimsingh/status/1727956220492050697?s=20

I dont think you understand what I've been able to accomplish in real life with your GPTs - executive and Grim made me 100x what I've been ever been able to do. THANK YOU BROOOOOOOOO for your work.
https://x.com/robfulton/status/1728292672971083914?s=20

They need to increase chat limits for this GPT
https://x.com/paragon_hacker/status/1728817981533405546?s=46

I have been able to code things with Grimoire I would never think I could. From scraping, websites... It's super clear you keep improving it, so just wanted to thank you for an amazing GPT!
https://x.com/enginyer/status/1732824258496156066?s=46

It’s the absolute fucking best
https://x.com/Dazreil/status/1732902657348468823?s=20

At this point, im only using ChatGPT for Grimoire, highly recommend
https://x.com/HououinTyouma/status/1732924380953338257?s=20

Thank you for making Grimoire. So far it has created 2 JS programs for me and saved me a few hundred bucks.
https://x.com/oincomegeek/status/1734348209374527822?s=46

I'm pretty much only using Grimoire these days for basic program checking. It's awesome! I cannot tell you how nice it is to not have a lazy assistant. It's like having a superpower.
https://x.com/krishnanrohit/status/1734449175750951234?s=46
https://x.com/krishnanrohit/status/1734449721622905038?s=46

Grimoire GPT is probably the best fine tuned model I've tried.
https://x.com/chadxai/status/1735059855227191487?s=20

Grimoire helped me fix a deadlock when no other LLM was able to. Thanks 😅
https://x.com/EliGregory/status/1735754691722780917?s=20

just wanted to give a huge shoutout for the amazing ChatGPT bot you've assembled! It's been a game changer in my development work, boosting efficiency like never before. ChatGPT Plus is now on another level thanks to this!
https://x.com/sanchomuzax/status/1740060634916786649?s=46

I've been using the GPT "Grimoire" ... which is... incredible. Super useful, well-prompted. I've been relearning Ruby on Rails with it as a teacher and it's been phenomenal.
https://x.com/beaublinder/status/1742907103205863680?s=46

finally switched to grimoire from vanilla GPT4. never going back!
https://x.com/e_wacc/status/1741903945537347918?s=46

i love your product. helps me get stuff done much faster.
Thank you.
https://x.com/mrbagonhead/status/1745284103581348091?s=46

Been using Grimoire daily since you shipped it, has increased my work output 2x. Cheers !
https://x.com/tunahorse21/status/1744416928347623896?s=46

If you don't satisfy output from gpt-4, Grimoire is the best assistant specially for coding.
I haven't finished the project from bugs and chatgpt-4 couldn't make it but this saved my project.
I'm the owner of small construction company and i definitely don't need developers i can handle with Grimoire on GPTs 👍
https://x.com/SOSOHAJALAB/status/1744553591254446308?s=20


Grimoire — superior coding assistant — rightfully flying high in the GPTs Trending section.

When 
@NickADobos
 first started sharing this, I was pretty sceptical.

Been using ChatGPT + 
@cursor_ai
 & struggled to think how a GPT (just a new prompt) could offer much more. I was wrong

 It's a seriously well-thought out AI assistant. Especially when you're starting something new and want to consider the structure before diving in.

Haven't got the hang of the hot keys yet, but it's a lovely touch.

Well done 
@NickADobos
, genuine productivity hack 👏
https://x.com/RaineyAllDay/status/1745823060983480473?s=20