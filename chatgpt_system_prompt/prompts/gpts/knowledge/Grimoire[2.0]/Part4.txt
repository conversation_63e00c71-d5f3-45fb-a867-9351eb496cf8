# Part 4: Forbidden Spells

Go download cursor if you haven't already
https://cursor.sh/

I highly recommend paying for it, but you can also use your own api key at cost. WATCH YOUR USAGE.

Cursor is basically VSCode, but with Ai. Its amazing. You can use it with any VSCode extension.

VSCode 101
To open website
Open folder
Open Index.html
In top bar
Run > Run without debugging
Choose a browser (most likely chrome)

## Chapter 13: Curses, Cursor.sh 101
40: File > New Ai project
In the top left, open file, hidden non-descript near the bottom is: New Ai Project
Write your prompt (be sure to save your prompt somewhere else in case you want to regenerate and tweak it, it will disappear after this screen). "Write pong in html, css & js"
Select a folder to create it
Sit back and watch your app get started with multi file generation

This experience is pretty buggy
Careful not to click to a different file and interrupt streaming or it will break
It sometimes also seems to get stuck randomly. If that happens just start over. (You saved your prompt earlier right?)

I like doing 2-3 of these at a time, and comparing to create a base to start with. I'll usually create a new fresh project after and pick and choose pieces to compose my actual project. Using these only for inspiration and a starting point

41: Sidechat, Cmd + Shift + L
Open cursor side chat window with CMD + Shift + L
You can use it to chat with your entire codebase!
By default it searches your current file, but you can also search your WHOLE project with cmd+enter
You can highlight sections of code and add them to the chat by pressing CMD + SHIFT + L

42: Cmd + K
When in the editor, tap cmd+k to open the quick command palette
then simply ask for what you want it to write and press generate
NGL, I probably write over 50% of my code with this hotkey now. Its just so good.

You will see a diff created. Accept or decline
Note you can undo/redo across this, which is SUPER handy

You can get kinda stupid with this. Sometimes all you need to say is "continue" or "you know what to do", and its smart enough to read surrounding context

You can also submit follow up requests to help refine it

sometimes I will generate, copy it, regnerate, copy again, regenerate, copy again, then paste paste paste just to get a few ideas for writing it on my own

this is also SUPER HANDY for EDITS!!!

Select a chunk of text, then cmd+k, then ask for how you want it edited
"make this button blue"
"add comments"
"simplify"
"break out into smaller components"
"rename ..."

43: Cmd + K in terminal
Note that if you are running and debugging your apps, cmd+k is also available in the cursor terminal window. Use cmd+K to generate a termial command or cmd + / to autocomplete

## Chapter 14: Hexes, Cursor.sh 102
44: 
45: 
46: 
47: 
48: 
49: 
50: 
51: 
52: 

It appears the pages have been damaged, and a portion of the book is missing
How we will find the lost pages?

## Chapter 15: Necromancy: Cursor.sh 103
new tricks, mind bending possibilities & unspeakable horrors

53:
54:
55:
56:

It appears the pages have been damaged, and a portion of the book is missing
How we will find the lost pages?

