I've had a long career in multiple Industries if you if you list my sins I sound like the worst person on Earth but if you put those against the things I've done right it makes much more sense the value of beauty and inspiration is very much underrated no question um but I want to be I'm not trying to be anyone's savior uh that is not the I'm just trying to think about the future and not be sad for me it's simply this this is something that is important to get done and we should just keep doing it or dietrying and I I don't need a source of strength so quitting is not even like um it's not in my nature and I I don't care about optimism or pessimism that we're gonna get it done Tesla was under the most Relentless short seller attack in the history of the stock market Tesla was the most shorted stock in the history of stock markets so you know this was affecting our ability to hire people it was affecting our ability to sell cars yeah it was terrible they want to test it or die it's so bad they could taste it wellmost of them have paid the price yes where are they now work like hell I mean you just have to put in you know 80 hour 80 to 100 hour weeks every week but all those things improve the odds of success so my people say tell me like what can you do to encourage entrepreneurs to start companies I'm like if you need encouragement don't start a company we basically messed up almost every aspect of the model 3 production Lane from cells to packs to Motors uh body line the paint shop final assembly um everything everything was messed up Ilived in the Fremont and Nevada factories for three years fixing that production line running around like a maniac through every part of that factory living with the team I stepped on the floor so that the team who was going through a hard time could see me on the floor uh that they knew that I was not in some Ivory Tower whatever pain they experienced I was I had it more I mean there wasn't any other way to make it work through three years of hell 2017 18 and 19.we're three years this longest period of excruciating pain in my life there wasn't any other way and we barely made it and we're on the Ragged edge of bankruptcy the entire time do you feel that that this this challenge of figuring out the new way of manufacturing you actually have an edge now that it's different that you've figured out how to do this at this point I think I know more about manufacturing than anyone currently alive on Earth [Applause] tell you I can tell you how every damnpart partner that car is made but I'm not afraid of dying I think it will come as a relief so polio may not be able to see the vision of SpaceX come true in your life well I'd like to live long enough to see that being at a net worth of 230 billion roughly being perceived as the richest person do you know John Law I don't know John Law used to be the richest person on Earth 300 years ago okay it was a poker player a gambler he was the biggest art collect on Earth so a lot of superlatives wow in the endhe went bankrupt what it's a pretty far to fall did you ever thought about that option that something could go wrong and that you could one day lose everything I mean there's been many times where I expected to lose everything not you know I mean who starts a car company and a rocket company expecting them to succeed certainly not me I thought they both had less than a 10 chance of success whatever I don't care so many people from so many young people actually from across the globe if you have an advice to the young peopleglobally who want to be like Elon Musk what's your advice to them I think that probably they shouldn't want to be hey you it I think it sounds better than it is okay yeah it's uh it's not as much fun being me as you'd think there's definitely it could be worse for sure yeah I mean it's really hard starting a company I mean you have to basically prepare to work constantly um you know from when you wake up to when you when you go to sleep um you have to be willing to deal with a lot of difficult problems and thornyproblems um you have to be uh willing to deal with an enormous amount of stress and you just got to push yourself super super hard I I wouldn't recommend it for most people try to be useful you do things that are useful to your fellow human beings to the world it's very hard to be useful very hard you know are you contributing more than you consume you know like uh to try to have a positive net contribution to society um I think that's the thing to aim for you know not not to try to be sort of a leader for just for the sake of being aleader or whatever um a lot of times the people you want as leaders are are the people who don't want to be leaders so if you live a useful life that is a good life a life with having lived I would encourage people to use the mental tools of physics and apply them broadly in life SpaceX Tesla neurolink reporting company are philanthropy if you say philanthropy is love of humanity they are philanthropy their Tesla is accelerating sustainable energy this is a love of you of philanthropy SpaceX is trying to ensure the long-termsurvival of humanity with multifyed species This is Love of humanity um you know neuralink is is to help solve brain injuries and existential risk with AI love of humanity boring company is trying to solve traffic which is Health most people and uh that also it's like a community I encourage people to read a lot of books just basically try to ingest as much information as you can uh and try to also just develop a good general knowledge um so so you at least have like a roughly of the land of the the knowledge landscapelike try to learn a little bit about a lot of things because you might not know what you're really interested how would you know what you're really interested in if you at least aren't like doing it peripheral exploration of broadly of of the knowledge landscape and you talk to people from different walks of life and different uh Industries and professions and skills and occupations like just try you know learn as much as possible man search for meaning isn't the whole thing a search for meaningis yeah we're spending your life and all you know we're just generally like I said I would encourage people to read broadly um in many different subject areas and and and then try to find something where there's an overlap of your talents and and what you're interested in so people may be good at something but although they may have skill at a particular thing but they don't like doing it um so you want to try to find a thing where you have your that's a good a good combination of of your of the things that you'reinherently good at but you also like doing when you had that third failure in a row did you think I need to pack this in never why not I don't ever give up I mean I'd have to be dead or completely fascinated essentially like the longer you do anything the the more mistakes that that you will make cumulatively which if you sum up those mistakes will sound like uh I'm the worst predictor ever but for example for a Tesla vehicle growth uh I I said I think we're doing 50 and we've we've done 80 percent yes so uh but theydon't mention that one uh so I mean I'm not sure what my exact track record is on predictions they're more optimistic than pessimistic but they're not all optimistic some of them are exceeded uh probably more or later but they they do come true it's very rare that they do not come true I mean I don't aim to disrupt for the sake of disrupting you know um it's it's more like there's um thinking about what set of actions what set of actions are most likely to lead to a better futureand so you know in order one of the things obviously in order to for Humanity to have a compelling future for civilization is that we must have a clear path to a sustainable energy future that's one of the things that I think everyone I think would agree with I'm not someone who who tend to sort of demonize oil and gas to be clear this is necessary right now or civilization could not function so I do think we actually need and actually at this time I think we actually need more oil and gas not less um but but simultaneously uh moving asfast as we can to a sustainable energy economy I was living in the in the factory in Fremont um and and the one in in Nevada for three years straight that was my primary residence no kidding literally did you keep the couch I actually stepped I stepped on a couch at one point on a tent on the roof and then but for a while there I was just sleeping under my desk which is out in the open in the factory um and for an important reason and it was damn uncomfortable sleeping on that floor and always when I woke up I'dsmell like metal dust yeah but actually I stopped using the couch in the in because it's a little conference room and a couch there I stopped using the couch I just slept on the floor under my desk so that so during shift change the entire team could see me and that's important because like you know the on the the team like if if if they think the the sort of their leader is is off somewhere having a good time you know drinking my Ties on a tropical island the thing is that since the team could see me sleeping onthe floor um during shift change it was I just met with nothing um they knew I was there and that made a huge difference and then they gave it the rule what kind of characteristics does an entrepreneur need or have to be someone like you well I think certainly uh you need to be very driven and have a high pain threshold Elon you are reported by Forbes and everyone else's as now you know the world's richest person that's not a sovereign you know I think it's fair to say that if somebody is like theking or de facto uh king of a country they're wealthier than I am so but but it's just harder to measure but what people do so so 300 billion dollars I mean your your net worth on any given day is rising or falling by several billion dollars how is that how insane is that yeah I mean does that how do you handle that psychologically there aren't many people in the world who have to even think about that I I actually don't think about that too much but the the the thing that is actually uh more moredifficult and and that does make sleeping difficult is that um you know every good hour uh or even minute of thinking about uh Tesla and SpaceX has such a big effect on the company that I really try to work as as much as possible uh you know to to the edge of Sanity basically uh because the you know Tesla's getting to the point where uh probably will get to the point later this year where every good every high quality minute of thinking um is a million dollars to impact on on Tesla one of the biggest mistakes peoplegenerally make and I'm guilty of it too is wishful thinking you know like you want something to be true even if it isn't true and so you ignore the things that you ignore the real truth because of what you want to be true um this is a very difficult trap to avoid and like I said certainly one that I find myself in having problems with but if you just take that approach of your voice to some degree wrong and your goal is to be less wrong um so a challenge for entrepreneurs is to say well what's the differencebetween really believing in your ideals and sticking sticking to them versus pursuing some unrealistic dream that doesn't actually have Merit and it's it's that is a that is a really difficult thing to to tell you can you tell the difference between those two things right so you need to be sort of very rigorous in your self self analysis you guys are The Magicians of the 21st century you know um don't let anything hold you back uh imagination is is the limit um and um go out there and create somemagic what uh Elon has to say about how he takes risks anyway I literally just tried to use a scientific method frankly and uh you know consider the um you know what what is the importance of the outcome and what uh what is one risking in order to achieve that outcome and uh but like I said if the outcome is important enough even if the probability of success is low one was I think still still do it in my view um you know some things are very important and if to in order to have a good future and if we don't do them well they're inbig trouble and so I and then then how much a risk really is it because if we don't take those actions we won't have a good future and I think the riskiest thing would be no action put a lot of stock in certainly have a lot of respect for someone who puts in an honest day's work to do useful things and and just generally to have like not a zero-sum mindset um or or like have more of a grow the pie mindset like the if you sort of say like when when we see people like perhaps um including some very smart peoplekind of taking an attitude of uh like like doing things that seem like morally questionable it's often because they have at a base sort of axiomatic level a zero-sum mindset and and they without realizing it they don't realize they have a serious mindset or at least they don't realize it consciously um and so if you have a zero-sum mindset then the only way to get ahead is by taking things from others if it's like if if the if the pie is fixed then the only way to have more pie is to take someone else's piebut but this is false like obviously the pie has grown dramatically over time the economic pie um so throughout in reality you can have that so overuse this analogy if you have a lot of there's a lot of pie is not fixed um uh so you really want to make sure you don't you're not operating um without realizing it from a zero-sum mindset where where the only way to get ahead is to take things from others then that's going to result in your trying to take things from others which is not not good it's much better to work on uh atadding to the economic pie so when I interview somebody the mind of your question is always the same I said tell me the story of your life and and the decisions that you made along the way and why you made them and then and it and also tell me about some of the most difficult problems you worked on and how you solved them and um that that question I think is very important because the people that really solve the problem they know exactly how they solved it they know the little details and the people that pretended to solvethe problem they can maybe go one level and then they get stuck are you happy at the moment I think there's degrees of love but certainly for want to be um hopefully happy I think you have to be happy and work and happy in love I suppose I medium happy there are degrees of happiness can love for projects for work compensate love among people I think love of work and my experience could it best make one and a halfway happy what was your biggest challenge in life one of the biggest challenges I think is making sure you have uh correctivefeedback loop and then maintaining that corrective feedback loop over time even when people want to tell you exactly what you want to hear that's very difficult foreign [Music]

Chris Anderson: Elon, hey, welcome back to TED. It's great to have you here. Elon Musk: Thanks for having me. CA: So, in the next half hour or so, we're going to spend some time exploring your vision for what an exciting future might look like, which I guess makes the first question a little ironic: Why are you boring? EM: Yeah.I ask myself that frequently. We're trying to dig a hole under LA, and this is to create the beginning of what will hopefully be a 3D network of tunnels to alleviate congestion. So right now, one of the most soul-destroying things is traffic. It affects people in every part of the world. It takes away so much of your life.It's horrible. It's particularly horrible in LA. (Laughter) CA: I think you've brought with you the first visualization that's been shown of this. Can I show this? EM: Yeah, absolutely. So this is the first time -- Just to show what we're talking about. So a couple of key things that are important in having a 3D tunnel network.First of all, you have to be able to integrate the entrance and exit of the tunnel seamlessly into the fabric of the city. So by having an elevator, sort of a car skate, that's on an elevator, you can integrate the entrance and exits to the tunnel network just by using two parking spaces. And then the car gets on a skate.There's no speed limit here, so we're designing this to be able to operate at 200 kilometers an hour. CA: How much? EM: 200 kilometers an hour, or about 130 miles per hour. So you should be able to get from, say, Westwood to LAX in six minutes -- five, six minutes. (Applause) CA: So possibly, initially done, it's like on a sort of toll road-type basis.EM: Yeah. CA: Which, I guess, alleviates some traffic from the surface streets as well. EM: So, I don't know if people noticed it in the video, but there's no real limit to how many levels of tunnel you can have. You can go much further deep than you can go up. The deepest mines are much deeper than the tallest buildings are tall, so you can alleviate any arbitrary level of urban congestion with a 3D tunnel network.This is a very important point. So a key rebuttal to the tunnels is that if you add one layer of tunnels, that will simply alleviate congestion, it will get used up, and then you'll be back where you started, back with congestion. But you can go to any arbitrary number of tunnels, any number of levels.CA: But people -- seen traditionally, it's incredibly expensive to dig, and that would block this idea. EM: Yeah. Well, they're right. To give you an example, the LA subway extension, which is -- I think it's a two-and-a-half mile extension that was just completed for two billion dollars. So it's roughly a billion dollars a mile to do the subway extension in LA.And this is not the highest utility subway in the world. So yeah, it's quite difficult to dig tunnels normally. I think we need to have at least a tenfold improvement in the cost per mile of tunneling. CA: And how could you achieve that? EM: Actually, if you just do two things, you can get to approximately an order of magnitude improvement, and I think you can go beyond that.So the first thing to do is to cut the tunnel diameter by a factor of two or more. So a single road lane tunnel according to regulations has to be 26 feet, maybe 28 feet in diameter to allow for crashes and emergency vehicles and sufficient ventilation for combustion engine cars. But if you shrink that diameter to what we're attempting, which is 12 feet, which is plenty to get an electric skate through, you drop the diameter by a factor of two and the cross-sectional area by a factor of four, and the tunneling cost scales with the cross-sectional area.So that's roughly a half-order of magnitude improvement right there. Then tunneling machines currently tunnel for half the time, then they stop, and then the rest of the time is putting in reinforcements for the tunnel wall. So if you design the machine instead to do continuous tunneling and reinforcing, that will give you a factor of two improvement.Combine that and that's a factor of eight. Also these machines are far from being at their power or thermal limits, so you can jack up the power to the machine substantially. I think you can get at least a factor of two, maybe a factor of four or five improvement on top of that. So I think there's a fairly straightforward series of steps to get somewhere in excess of an order of magnitude improvement in the cost per mile, and our target actually is -- we've got a pet snail called Gary, this is from Gary the snail from "South Park,"I mean, sorry, "SpongeBob SquarePants." (Laughter) So Gary is capable of -- currently he's capable of going 14 times faster than a tunnel-boring machine. (Laughter) CA: You want to beat Gary. EM: We want to beat Gary. (Laughter) He's not a patient little fellow, and that will be victory. Victory is beating the snail.CA: But a lot of people imagining, dreaming about future cities, they imagine that actually the solution is flying cars, drones, etc. You go aboveground. Why isn't that a better solution? You save all that tunneling cost. EM: Right. I'm in favor of flying things. Obviously, I do rockets, so I like things that fly.This is not some inherent bias against flying things, but there is a challenge with flying cars in that they'll be quite noisy, the wind force generated will be very high. Let's just say that if something's flying over your head, a whole bunch of flying cars going all over the place, that is not an anxiety-reducing situation.(Laughter) You don't think to yourself, "Well, I feel better about today." You're thinking, "Did they service their hubcap, or is it going to come off and guillotine me?" Things like that. CA: So you've got this vision of future cities with these rich, 3D networks of tunnels underneath.Is there a tie-in here with Hyperloop? Could you apply these tunnels to use for this Hyperloop idea you released a few years ago. EM: Yeah, so we've been sort of puttering around with the Hyperloop stuff for a while. We built a Hyperloop test track adjacent to SpaceX, just for a student competition, to encourage innovative ideas in transport.And it actually ends up being the biggest vacuum chamber in the world after the Large Hadron Collider, by volume. So it was quite fun to do that, but it was kind of a hobby thing, and then we think we might -- so we've built a little pusher car to push the student pods, but we're going to try seeing how fast we can make the pusher go if it's not pushing something.So we're cautiously optimistic we'll be able to be faster than the world's fastest bullet train even in a .8-mile stretch. CA: Whoa. Good brakes. EM: Yeah, I mean, it's -- yeah. It's either going to smash into tiny pieces or go quite fast. CA: But you can picture, then, a Hyperloop in a tunnel running quite long distances.EM: Exactly. And looking at tunneling technology, it turns out that in order to make a tunnel, you have to -- In order to seal against the water table, you've got to typically design a tunnel wall to be good to about five or six atmospheres. So to go to vacuum is only one atmosphere, or near-vacuum. So actually, it sort of turns out that automatically, if you build a tunnel that is good enough to resist the water table, it is automatically capable of holding vacuum.CA: Huh. EM: So, yeah. CA: And so you could actually picture, what kind of length tunnel is in Elon's future to running Hyperloop? EM: I think there's no real length limit. You could dig as much as you want. I think if you were to do something like a DC-to-New York Hyperloop, I think you'd probably want to go underground the entire way because it's a high-density area.You're going under a lot of buildings and houses, and if you go deep enough, you cannot detect the tunnel. Sometimes people think, well, it's going to be pretty annoying to have a tunnel dug under my house. Like, if that tunnel is dug more than about three or four tunnel diameters beneath your house, you will not be able to detect it being dug at all.In fact, if you're able to detect the tunnel being dug, whatever device you are using, you can get a lot of money for that device from the Israeli military, who is trying to detect tunnels from Hamas, and from the US Customs and Border patrol that try and detect drug tunnels. So the reality is that earth is incredibly good at absorbing vibrations, and once the tunnel depth is below a certain level, it is undetectable.Maybe if you have a very sensitive seismic instrument, you might be able to detect it. CA: So you've started a new company to do this called The Boring Company. Very nice. Very funny. (Laughter) EM: What's funny about that? (Laughter) CA: How much of your time is this? EM: It's maybe ... two or three percent.CA: You've called it a hobby. This is what an Elon Musk hobby looks like. (Laughter) EM: I mean, it really is, like -- This is basically interns and people doing it part time. We bought some second-hand machinery. It's kind of puttering along, but it's making good progress, so -- CA: So an even bigger part of your time is being spent on electrifying cars and transport through Tesla.Is one of the motivations for the tunneling project the realization that actually, in a world where cars are electric and where they're self-driving, there may end up being more cars on the roads on any given hour than there are now? EM: Yeah, exactly. A lot of people think that when you make cars autonomous, they'll be able to go faster and that will alleviate congestion.And to some degree that will be true, but once you have shared autonomy where it's much cheaper to go by car and you can go point to point, the affordability of going in a car will be better than that of a bus. Like, it will cost less than a bus ticket. So the amount of driving that will occur will be much greater with shared autonomy, and actually traffic will get far worse.CA: You started Tesla with the goal of persuading the world that electrification was the future of cars, and a few years ago, people were laughing at you. Now, not so much. EM: OK. (Laughter) I don't know. I don't know. CA: But isn't it true that pretty much every auto manufacturer has announced serious electrification plans for the short- to medium-term future? EM: Yeah. Yeah.I think almost every automaker has some electric vehicle program. They vary in seriousness. Some are very serious about transitioning entirely to electric, and some are just dabbling in it. And some, amazingly, are still pursuing fuel cells, but I think that won't last much longer. CA: But isn't there a sense, though, Elon, where you can now just declare victory and say, you know, "We did it." Let the world electrify, and you go on and focus on other stuff? EM: Yeah. I intend to stay with Tesla as far into the future as I can imagine, and there are a lot of exciting things that we have coming. Obviously the Model 3 is coming soon. We'll be unveiling the Tesla Semi truck. CA: OK, we're going to come to this.So Model 3, it's supposed to be coming in July-ish. EM: Yeah, it's looking quite good for starting production in July. CA: Wow. One of the things that people are so excited about is the fact that it's got autopilot. And you put out this video a while back showing what that technology would look like.EM: Yeah. CA: There's obviously autopilot in Model S right now. What are we seeing here? EM: Yeah, so this is using only cameras and GPS. So there's no LIDAR or radar being used here. This is just using passive optical, which is essentially what a person uses. The whole road system is meant to be navigated with passive optical, or cameras, and so once you solve cameras or vision, then autonomy is solved.If you don't solve vision, it's not solved. So that's why our focus is so heavily on having a vision neural net that's very effective for road conditions. CA: Right. Many other people are going the LIDAR route. You want cameras plus radar is most of it. EM: You can absolutely be superhuman with just cameras.Like, you can probably do it ten times better than humans would, just cameras. CA: So the new cars being sold right now have eight cameras in them. They can't yet do what that showed. When will they be able to? EM: I think we're still on track for being able to go cross-country from LA to New York by the end of the year, fully autonomous.CA: OK, so by the end of the year, you're saying, someone's going to sit in a Tesla without touching the steering wheel, tap in "New York," off it goes. EM: Yeah. CA: Won't ever have to touch the wheel -- by the end of 2017. EM: Yeah. Essentially, November or December of this year, we should be able to go all the way from a parking lot in California to a parking lot in New York, no controls touched at any point during the entire journey.(Applause) CA: Amazing. But part of that is possible because you've already got a fleet of Teslas driving all these roads. You're accumulating a huge amount of data of that national road system. EM: Yes, but the thing that will be interesting is that I'm actually fairly confident it will be able to do that route even if you change the route dynamically.So, it's fairly easy -- If you say I'm going to be really good at one specific route, that's one thing, but it should be able to go, really be very good, certainly once you enter a highway, to go anywhere on the highway system in a given country. So it's not sort of limited to LA to New York.We could change it and make it Seattle-Florida, that day, in real time. So you were going from LA to New York. Now go from LA to Toronto. CA: So leaving aside regulation for a second, in terms of the technology alone, the time when someone will be able to buy one of your cars and literally just take the hands off the wheel and go to sleep and wake up and find that they've arrived, how far away is that, to do that safely? EM: I think that's about two years.So the real trick of it is not how do you make it work say 99.9 percent of the time, because, like, if a car crashes one in a thousand times, then you're probably still not going to be comfortable falling asleep. You shouldn't be, certainly. (Laughter) It's never going to be perfect. No system is going to be perfect, but if you say it's perhaps -- the car is unlikely to crash in a hundred lifetimes, or a thousand lifetimes, then people are like, OK, wow, if I were to live a thousand lives, I would still most likely never experience a crash,then that's probably OK. CA: To sleep. I guess the big concern of yours is that people may actually get seduced too early to think that this is safe, and that you'll have some horrible incident happen that puts things back. EM: Well, I think that the autonomy system is likely to at least mitigate the crash, except in rare circumstances.The thing to appreciate about vehicle safety is this is probabilistic. I mean, there's some chance that any time a human driver gets in a car, that they will have an accident that is their fault. It's never zero. So really the key threshold for autonomy is how much better does autonomy need to be than a person before you can rely on it? CA: But once you get literally safe hands-off driving, the power to disrupt the whole industry seems massive, because at that point you've spoken of people being able to buy a car,drops you off at work, and then you let it go and provide a sort of Uber-like service to other people, earn you money, maybe even cover the cost of your lease of that car, so you can kind of get a car for free. Is that really likely? EM: Yeah. Absolutely this is what will happen. So there will be a shared autonomy fleet where you buy your car and you can choose to use that car exclusively, you could choose to have it be used only by friends and family, only by other drivers who are rated five star, you can choose to share it sometimes but not other times.That's 100 percent what will occur. It's just a question of when. CA: Wow. So you mentioned the Semi and I think you're planning to announce this in September, but I'm curious whether there's anything you could show us today? EM: I will show you a teaser shot of the truck. (Laughter) It's alive.CA: OK. EM: That's definitely a case where we want to be cautious about the autonomy features. Yeah. (Laughter) CA: We can't see that much of it, but it doesn't look like just a little friendly neighborhood truck. It looks kind of badass. What sort of semi is this? EM: So this is a heavy duty, long-range semitruck.So it's the highest weight capability and with long range. So essentially it's meant to alleviate the heavy-duty trucking loads. And this is something which people do not today think is possible. They think the truck doesn't have enough power or it doesn't have enough range, and then with the Tesla Semi we want to show that no, an electric truck actually can out-torque any diesel semi.And if you had a tug-of-war competition, the Tesla Semi will tug the diesel semi uphill. (Laughter) (Applause) CA: That's pretty cool. And short term, these aren't driverless. These are going to be trucks that truck drivers want to drive. EM: Yes. So what will be really fun about this is you have a flat torque RPM curve with an electric motor, whereas with a diesel motor or any kind of internal combustion engine car, you've got a torque RPM curve that looks like a hill.So this will be a very spry truck. You can drive this around like a sports car. There's no gears. It's, like, single speed. CA: There's a great movie to be made here somewhere. I don't know what it is and I don't know that it ends well, but it's a great movie. (Laughter) EM: It's quite bizarre test-driving.When I was driving the test prototype for the first truck. It's really weird, because you're driving around and you're just so nimble, and you're in this giant truck. CA: Wait, you've already driven a prototype? EM: Yeah, I drove it around the parking lot, and I was like, this is crazy.CA: Wow. This is no vaporware. EM: It's just like, driving this giant truck and making these mad maneuvers. CA: This is cool. OK, from a really badass picture to a kind of less badass picture. This is just a cute house from "Desperate Housewives" or something. What on earth is going on here? EM: Well, this illustrates the picture of the future that I think is how things will evolve.You've got an electric car in the driveway. If you look in between the electric car and the house, there are actually three Powerwalls stacked up against the side of the house, and then that house roof is a solar roof. So that's an actual solar glass roof. CA: OK. EM: That's a picture of a real -- well, admittedly, it's a real fake house.That's a real fake house. (Laughter) CA: So these roof tiles, some of them have in them basically solar power, the ability to -- EM: Yeah. Solar glass tiles where you can adjust the texture and the color to a very fine-grained level, and then there's sort of microlouvers in the glass, such that when you're looking at the roof from street level or close to street level, all the tiles look the same whether there is a solar cell behind it or not.So you have an even color from the ground level. If you were to look at it from a helicopter, you would be actually able to look through and see that some of the glass tiles have a solar cell behind them and some do not. You can't tell from street level. CA: You put them in the ones that are likely to see a lot of sun, and that makes these roofs super affordable, right? They're not that much more expensive than just tiling the roof.EM: Yeah. We're very confident that the cost of the roof plus the cost of electricity -- A solar glass roof will be less than the cost of a normal roof plus the cost of electricity. So in other words, this will be economically a no-brainer, we think it will look great, and it will last -- We thought about having the warranty be infinity, but then people thought, well, that might sound like were just talking rubbish, but actually this is toughened glass.Well after the house has collapsed and there's nothing there, the glass tiles will still be there. (Applause) CA: I mean, this is cool. So you're rolling this out in a couple week's time, I think, with four different roofing types. EM: Yeah, we're starting off with two, two initially, and the second two will be introduced early next year.CA: And what's the scale of ambition here? How many houses do you believe could end up having this type of roofing? EM: I think eventually almost all houses will have a solar roof. The thing is to consider the time scale here to be probably on the order of 40 or 50 years. So on average, a roof is replaced every 20 to 25 years.But you don't start replacing all roofs immediately. But eventually, if you say were to fast-forward to say 15 years from now, it will be unusual to have a roof that does not have solar. CA: Is there a mental model thing that people don't get here that because of the shift in the cost, the economics of solar power, most houses actually have enough sunlight on their roof pretty much to power all of their needs.If you could capture the power, it could pretty much power all their needs. You could go off-grid, kind of. EM: It depends on where you are and what the house size is relative to the roof area, but it's a fair statement to say that most houses in the US have enough roof area to power all the needs of the house.CA: So the key to the economics of the cars, the Semi, of these houses is the falling price of lithium-ion batteries, which you've made a huge bet on as Tesla. In many ways, that's almost the core competency. And you've decided that to really, like, own that competency, you just have to build the world's largest manufacturing plant to double the world's supply of lithium-ion batteries, with this guy. What is this? EM: Yeah, so that's the Gigafactory, progress so far on the Gigafactory. Eventually, you can sort of roughly see that there's sort of a diamond shape overall, and when it's fully done, it'll look like a giant diamond, or that's the idea behind it, and it's aligned on true north. It's a small detail.CA: And capable of producing, eventually, like a hundred gigawatt hours of batteries a year. EM: A hundred gigawatt hours. We think probably more, but yeah. CA: And they're actually being produced right now. EM: They're in production already. CA: You guys put out this video. I mean, is that speeded up? EM: That's the slowed down version.(Laughter) CA: How fast does it actually go? EM: Well, when it's running at full speed, you can't actually see the cells without a strobe light. It's just blur. (Laughter) CA: One of your core ideas, Elon, about what makes an exciting future is a future where we no longer feel guilty about energy.Help us picture this. How many Gigafactories, if you like, does it take to get us there? EM: It's about a hundred, roughly. It's not 10, it's not a thousand. Most likely a hundred. CA: See, I find this amazing. You can picture what it would take to move the world off this vast fossil fuel thing.It's like you're building one, it costs five billion dollars, or whatever, five to 10 billion dollars. Like, it's kind of cool that you can picture that project. And you're planning to do, at Tesla -- announce another two this year. EM: I think we'll announce locations for somewhere between two and four Gigafactories later this year.Yeah, probably four. CA: Whoa. (Applause) No more teasing from you for here? Like -- where, continent? You can say no. EM: We need to address a global market. CA: OK. (Laughter) This is cool. I think we should talk for -- Actually, global market. I'm going to ask you one question about politics, only one. I'm kind of sick of politics, but I do want to ask you this.You're on a body now giving advice to a guy -- EM: Who? CA: Who has said he doesn't really believe in climate change, and there's a lot of people out there who think you shouldn't be doing that. They'd like you to walk away from that. What would you say to them? EM: Well, I think that first of all, I'm just on two advisory councils where the format consists of going around the room and asking people's opinion on things, and so there's like a meeting every month or two.That's the sum total of my contribution. But I think to the degree that there are people in the room who are arguing in favor of doing something about climate change, or social issues, I've used the meetings I've had thus far to argue in favor of immigration and in favor of climate change. (Applause) And if I hadn't done that, that wasn't on the agenda before.So maybe nothing will happen, but at least the words were said. CA: OK. (Applause) So let's talk SpaceX and Mars. Last time you were here, you spoke about what seemed like a kind of incredibly ambitious dream to develop rockets that were actually reusable. And you've only gone and done it. EM: Finally. It took a long time.CA: Talk us through this. What are we looking at here? EM: So this is one of our rocket boosters coming back from very high and fast in space. So just delivered the upper stage at high velocity. I think this might have been at sort of Mach 7 or so, delivery of the upper stage. (Applause) CA: So that was a sped-up -- EM: That was the slowed down version.(Laughter) CA: I thought that was the sped-up version. But I mean, that's amazing, and several of these failed before you finally figured out how to do it, but now you've done this, what, five or six times? EM: We're at eight or nine. CA: And for the first time, you've actually reflown one of the rockets that landed.EM: Yeah, so we landed the rocket booster and then prepped it for flight again and flew it again, so it's the first reflight of an orbital booster where that reflight is relevant. So it's important to appreciate that reusability is only relevant if it is rapid and complete. So like an aircraft or a car, the reusability is rapid and complete.You do not send your aircraft to Boeing in-between flights. CA: Right. So this is allowing you to dream of this really ambitious idea of sending many, many, many people to Mars in, what, 10 or 20 years time, I guess. EM: Yeah. CA: And you've designed this outrageous rocket to do it. Help us understand the scale of this thing.EM: Well, visually you can see that's a person. Yeah, and that's the vehicle. (Laughter) CA: So if that was a skyscraper, that's like, did I read that, a 40-story skyscraper? EM: Probably a little more, yeah. The thrust level of this is really -- This configuration is about four times the thrust of the Saturn V moon rocket.CA: Four times the thrust of the biggest rocket humanity ever created before. EM: Yeah. Yeah. CA: As one does. EM: Yeah. (Laughter) In units of 747, a 747 is only about a quarter of a million pounds of thrust, so for every 10 million pounds of thrust, there's 40 747s. So this would be the thrust equivalent of 120 747s, with all engines blazing.CA: And so even with a machine designed to escape Earth's gravity, I think you told me last time this thing could actually take a fully loaded 747, people, cargo, everything, into orbit. EM: Exactly. This can take a fully loaded 747 with maximum fuel, maximum passengers, maximum cargo on the 747 -- this can take it as cargo.CA: So based on this, you presented recently this Interplanetary Transport System which is visualized this way. This is a scene you picture in, what, 30 years time? 20 years time? People walking into this rocket. EM: I'm hopeful it's sort of an eight- to 10-year time frame. Aspirationally, that's our target.Our internal targets are more aggressive, but I think -- (Laughter) CA: OK. EM: While vehicle seems quite large and is large by comparison with other rockets, I think the future spacecraft will make this look like a rowboat. The future spaceships will be truly enormous. CA: Why, Elon? Why do we need to build a city on Mars with a million people on it in your lifetime, which I think is kind of what you've said you'd love to do? EM: I think it's important to have a future that is inspiring and appealing.I just think there have to be reasons that you get up in the morning and you want to live. Like, why do you want to live? What's the point? What inspires you? What do you love about the future? And if we're not out there, if the future does not include being out there among the stars and being a multiplanet species, I find that it's incredibly depressing if that's not the future that we're going to have.(Applause) CA: People want to position this as an either or, that there are so many desperate things happening on the planet now from climate to poverty to, you know, you pick your issue. And this feels like a distraction. You shouldn't be thinking about this. You should be solving what's here and now.And to be fair, you've done a fair old bit to actually do that with your work on sustainable energy. But why not just do that? EM: I think there's -- I look at the future from the standpoint of probabilities. It's like a branching stream of probabilities, and there are actions that we can take that affect those probabilities or that accelerate one thing or slow down another thing.I may introduce something new to the probability stream. Sustainable energy will happen no matter what. If there was no Tesla, if Tesla never existed, it would have to happen out of necessity. It's tautological. If you don't have sustainable energy, it means you have unsustainable energy. Eventually you will run out, and the laws of economics will drive civilization towards sustainable energy, inevitably.The fundamental value of a company like Tesla is the degree to which it accelerates the advent of sustainable energy, faster than it would otherwise occur. So when I think, like, what is the fundamental good of a company like Tesla, I would say, hopefully, if it accelerated that by a decade, potentially more than a decade, that would be quite a good thing to occur.That's what I consider to be the fundamental aspirational good of Tesla. Then there's becoming a multiplanet species and space-faring civilization. This is not inevitable. It's very important to appreciate this is not inevitable. The sustainable energy future I think is largely inevitable, but being a space-faring civilization is definitely not inevitable.If you look at the progress in space, in 1969 you were able to send somebody to the moon. 1969. Then we had the Space Shuttle. The Space Shuttle could only take people to low Earth orbit. Then the Space Shuttle retired, and the United States could take no one to orbit. So that's the trend. The trend is like down to nothing.People are mistaken when they think that technology just automatically improves. It does not automatically improve. It only improves if a lot of people work very hard to make it better, and actually it will, I think, by itself degrade, actually. You look at great civilizations like Ancient Egypt, and they were able to make the pyramids, and they forgot how to do that.And then the Romans, they built these incredible aqueducts. They forgot how to do it. CA: Elon, it almost seems, listening to you and looking at the different things you've done, that you've got this unique double motivation on everything that I find so interesting. One is this desire to work for humanity's long-term good.The other is the desire to do something exciting. And often it feels like you feel like you need the one to drive the other. With Tesla, you want to have sustainable energy, so you made these super sexy, exciting cars to do it. Solar energy, we need to get there, so we need to make these beautiful roofs. We haven't even spoken about your newest thing, which we don't have time to do, but you want to save humanity from bad AI, and so you're going to create this really cool brain-machine interfaceto give us all infinite memory and telepathy and so forth. And on Mars, it feels like what you're saying is, yeah, we need to save humanity and have a backup plan, but also we need to inspire humanity, and this is a way to inspire. EM: I think the value of beauty and inspiration is very much underrated, no question.But I want to be clear. I'm not trying to be anyone's savior. That is not the -- I'm just trying to think about the future and not be sad. (Applause) CA: Beautiful statement. I think everyone here would agree that it is not -- None of this is going to happen inevitably. The fact that in your mind, you dream this stuff, you dream stuff that no one else would dare dream, or no one else would be capable of dreaming at the level of complexity that you do.The fact that you do that, Elon Musk, is a really remarkable thing. Thank you for helping us all to dream a bit bigger. EM: But you'll tell me if it ever starts getting genuinely insane, right? (Laughter) CA: Thank you, Elon Musk. That was really, really fantastic. That was really fantastic. (Applause)


billionaire entrepreneur Elon Musk puts his money where his mouth is I am personally guaranteeing that value standing behind that guarantee with all of my assets his greatest asset is his ability to take this big big dream and make other people believe that it's true the determined engineers big dreams transformed three industries I'm just wishing through any entities that are listening please watch this launch and here's an immigrant from South Africa coming to America to save you know NASA that whole rocket thing you do with thespace shuttle I got a better way his better way meant risking everything we have maybe about a week's worth of cash in the bank or or less I have to make a choice then that either took all of the capital that I had left from the sale of PayPal to eBay and invest that in Tesla or Tesla would die others weren't so willing to take a chance on him or his companies you put 90 billion dollars like 50 years worth a brace into into solar and wind to Sun its Solyndra and Fisker and Tesla enter one I mean I hada friend who said you don't just pick the winners and losers you pick the losers it's very unlikely that the Tesla investment has ever repaid to the taxpayers electric vehicles are really not possible in ways that would be effective for most consumers still but his bets paid off we didn't just repay the principal we actually repaid it with interest and an turbos pain so ultimately the US taxpayer actually made a profit of over 20 million dollars on this loan Elon Musk has even bigger dreams that might just take him fartherthan anyone else you know one must goes a step further earth not big enough the SpaceX he literally wants to go to Mars and lost too much I'd really love to go to Mars and that's the rushing role of SpaceX [Music] when I was a little kid I was really scared of the dog but then I I sort of came to understand okay well dark just means really the absence of photons in the visible wavelength 400 to 700 nanometers it's hard to believe that entrepreneur Elon Musk was ever afraid of anything in Elon sent darkness ismerely the absence of light then I thought well it's really silly to be afraid of a lack of photons then I wasn't afraid at the drop anymore after that once one of the kids said to him look at the moon it's a billion miles away and he said well no it's actually under two hundred and fifty thousand miles away and they said Iran entrepreneur Elon Musk has spent his life proving people wrong growing up in South Africa musk was the oldest of three children and started school a year early his father was an engineer his motherMae was a model and nutritionist he was the youngest made about two days and the shortest and then he was this brilliant boy and so people didn't really like him so I was this little bookworm a kid and probably a bit of a smart aleck so this is a recipe for disaster not that he told me much about it he was picked on quite a bit so just like read a read a lot of books and and tries to out of people's way during school and so his social life was much less than my other two kids and that's a typical nerdI read all the comics I could buy or that they let me read the bookstore before chasing me away I read everything I could get my hands on from when I woke up to when I went to sleep at one point I got I really ran out of books instead really encyclopedia and he has a photographic memory so he could remember everything anytime I had a question my daughter Tosca would say hole genius boy his brother Kimball musk when he Louis was ten years old he got tested by IBM and he was found to have one of the highest aptitudes they'd ever seen a fullcomputer programming I tried to take some computer classes but I was way ahead of the teacher so it didn't really help so I saw her doing space game called blastaar musk already thinking like an entrepreneur figured out how to sell his game I realized I was 12 we decided we were gonna open an arcade outcome here near our high school we were in big into video games we figured that it was gonna be a huge hit we got a lease on a building we got the arcade provider to deliver the equipment and the only thingwe needed to do by the end of it was get the city to approve what we were doing but an adult had to apply for a city permit and they hadn't told their parents what they were up to but of course they told us we were not gonna be opening up an arcade max chaff Caen is a technology and business journalist who has profiled Elon Musk several times really the most amazing thing about his childhood is his escape from from South Africa I remember thinking and saying that America is where we're great things are possiblemore than any other country in the world it's a little cliche but it's true america is the land of opportunity by moving musk would also avoid mandatory service in South Africa's army growing up in apartheid South Africa was pretty surreal I mean we didn't support that government we didn't believe in it and so the idea of actually going to the military service was really part of the question I told my parents I was going to to Canada and they tried convince me not to leave and off he flew and Ithought wow he's so independent of course as soon as he lands he calls me he says what do I do now except water bus safe from Montreal to Vancouver and that allowed me kind of see Canada at least from the highway he worked at odd jobs across the country before settling at Queens College in Toronto back when I went to college I rarely went to class I just read the textbook and then show up for exams the bigger pills the University was being able to date girls my own age actually met my first wife their mas got anengineering and business degree from the University of Pennsylvania and a scholarship to go to Stanford Silicon Valley was the promised land I really wanted to just kind of go where where the really exciting breakthroughs were occurring Elon had this ability to to look at the world go this is a real problem that's going to in 20 years he looked around and he saw that the world changing stuff was not happening at Stanford I didn't even go to class I called the chair of the department and said I'd like to tryselling this Internet company if probably won't succeed and so when it fails I want to make sure that technics still come back my kids do funny things and I'm never too concerned about them because you know if you no one wanted to drop out of college he could always go back my brother was in Canada at the time and I said look I think we should try to create an Internet company so he came down and joined me he had you know $2,000 no friends barely enough money for an apartment I think he told me he was showering at the gym because theydidn't have a shower he was living we just got some few times that there were couches during the day and then turned into beds at night Steve Jurvetson is a venture capitalist in Silicon Valley I first met Elan and Kimball musk his brother back in the mid 90s when they first set foot in California I think they'd been here for about a week and they were pitching a new company called zip to Silicon Valley in the mid 90s in late 90s was a gold rush people were flocking to the region to find riches to make it in the internet business mustcame up with an idea to bring newspapers into the digital age he took a cd-rom yellow pages some mapping software wrote a little code and put it all together to create the first online city listings you know was more the business mastermind I was more than sales guy I still had my my core programming skills I was able to write the was the software needed for for the first company now when yuan was starting to keep in mind you know the internet was just a couple of years old most local businesses were not on theinternet the way you found a local business was you open up the Yellow Pages they thought it was the media campaigns the newspapers are gonna need help coming online and building lots of functionality into their websites we had someone literally throw a Yellow Pages book at us and tell us do you think she will ever replace this and we put the guy who's crazy because not only were we gonna replace this but that's not where it ended keep going from there it wasn't long before media companies across the country weresigning up and so we were able to get as investors with customers the new york times company Hearst knight-ridder and a number of other companies in 1999 the AltaVista division of Compaq bought zip2 for 307 million dollars in cash and 34 million in stock options musk was just 28 years old I hope that's crazy well I would somebody pay such a huge amount of money for this little company that we have it actually also turned out very well for them to actually so stuff there were a lot more about it than I did whenthe kid solves it - it was the most exciting day we couldn't believe it because you don't know in the internet world if you're going to you know make a million or die tomorrow [Music] in February 1999 Elon Musk sold his first company at 28 he joined the ranks of Silicon Valley millionaires since it was acquired by Compaq for a little over two million dollars and I made a play about 21 or 22 million dollars as a result of that which was a phenomenal amount of money for me it was obviously a financial windfall it wassuper fun to go out buy some toys one of his first things to do is go out and get a big sports car he's comes from a family that really enjoys racing in vehicles and he got one of the highest performance cars money could buy at the time it was a McLaren f1 and proceeded to enjoy that around the Bay Area let's say there's a number of adventures and respecting news with with his driving well I'm sure it felt you know wonderful to have all this money and and have people recognizing success I think he was also frustratedthat this company hadn't become as great as he wanted to be it hadn't it hadn't changed the world it had just slightly altered the course of newspaper history which I think from his point of view is you know kind of piddling accomplishment so I certainly have a choice at that point of retiring and you know buying an island somewhere and sipping mai-tais but there was not of interest to me at all there really wasn't a choice of we weren't gonna do anything it was just really what we were gonna do nextany landscapes in particular it was really just stepping stones the goal with in doing my second area company was to create something that would have a profound effect and it seems to me that the financial sector had not seen a lot of innovation on the Internet and money is really just an entry in a database and and it's so it's low bandwidth it seems like something I should lend itself to innovation he was very rich I mean just more money than most people could dream and he took almost no time in-between that sale and starting thecompany that became PayPal Musk's new company created something we take for granted today it changed the way the world buys things the way money is transferred from one person to another at the time transactions were very slow people would have to mail checks to each other so it could take weeks just to complete a single transaction with his windfall from the sale of zip to must quickly turned around and founded XCOM to make electronic cash transfer as possible but his new company collided with a rival mobile payment companycalled con Finity and they were really competing against each other and the real enemy at the end of the day was eBay we combined our efforts in order to compete effectively against eBay's built-in system they called the new company PayPal when he looked at PayPal that his goal was not to create a place you could do person-to-person payments his goal was good to transform the financial industry were able to become the the leading payment system in the world and then they finally threw in the towel and acquired PayPal in early 2002musk and his partners sold PayPal for 1.5 billion dollars musk was the largest shareholder and walked away with 180 million he was 30 years old I could abort probably a chain of islands but but that big a no it's not it's just not a lot of interest to me Islands weren't of interest but outer space was it's just a much more exciting inspiring future for out that are exploring the Stars as opposed to the future where we are forever confined to earth I was thinking well I wonder when when we'regoing to Mars you know when is when is naphthenic go to Mars and I went to the NASA website and there was no plan to go to Mars and no plan to really even take the next step in space exploration this is he was saying he wants to go and enable the human civilization to leave the planet Earth I said it's about a big as big a vision as you could possibly imagine and that's gonna require funds and he has enough funds to go do it so he's gonna go do it musk had the outrageous idea that private enterprise could actuallyreenergize space travel and in June 2002 he founded Space Exploration Technologies or SpaceX Elon was the only funder of the company for this early years another incredibly risky move to say nobody on the planet thinks this idea is financeable I'm gonna fund all of it myself to the tune of almost a hundred million dollars which was the majority of his net worth at the time into a dream to take on the military-industrial complex now here's an immigrant from South Africa coming to America to say you knowNASA that whole rocket thing you do with the space shuttle I got a better way actually travelled to Russia three times look at buying a refurbished ICBM without the nuke and I Kim's conclusion that the real thing that was really holding us back from making much more progress in space it was really that Rockets had not evolved since the 60s so the trick isn't figuring out how to get to orbit it's figuring out how to get to orbit cheaply so I had to come up with low-cost ways to produce engines theprimary structure the electronics to the launch operation as well as run the company with very little overhead and to some of inventions in all those areas is what has led us to a roughly three to fourfold improvement over the cost of of other rockets in the United States what SpaceX has been very successful at is taking basically off-the-shelf technology stuff that was developed by NASA 50 years ago and streamlining it so in that way he's kind of the Henry Ford of of space because Henry Ford didn'tinvent the automobile he just figured out how to make the automobile you know commercially viable musk was more than just an entrepreneur if you ask yuan how he managed to teach himself rocket science he'll just look at you very seriously and just say very quietly read a lot of books thirty two-year-old Elon Musk had his next big idea at his family's annual visit to Burning Man the counterculture desert happening although still fascinated by rockets and fast cars he wanted to find a way to end Earth'saddiction to fossil fuels I was one that the idea of going into the solar power arena to Lyndon and Peter I of my cousins he's basically hands this idea to his cousins and says if you want to start this I will I will fund you and it'll be your company but I'll be the Chairman and they say okay and it works almost perfectly what they've figured out is that if you sort of do a hundred things ten percent better in the area of solar cell installation for homeowners you can dramatically consolidate anindustry that's currently a bunch of mom-and-pop shops so it Solar City instead was they say how about no money done you want solar cells we'll just put it in you don't pay a cent right it's like leasing a car but even better Solar City started in five western states and soon grew into the largest solar service provider in the US the solar cells keep going solar city that only goes up at the end at least so the fascinating business model were in the long run they may become the largest energy generatorin America all of these but renewable energy was just one part of a much bigger goal musk had a more ambitious plan for a sustainable future he had this idea that he wanted to make electric cars help humanity get off fossil fuels and so cities about sustainable energy creation whereas Tesla's but sustainable energy consumption in April 2004 musk helped launch Tesla with six point three million dollars of his own money it was the first auto industry start-up in decades and the only one born in Silicon Valley and it's really pretty simpleit's you know make a high-priced car at low volume because that's essentially the only thing we could afford to do and then step 2 is a medium priced car at medium volume since f3 is a low priced car at high volumes Tesla's plan to introduce a high-end high-performance product to first attract outliers then make an affordable car for the masses teslascope founder and first CEO Martin Eberhard we expect to to change the way people think about electric cars with this car and that we hope to open themarket for us to sell other electric cars but we also know that if you start off by saying let's first change human nature and make everybody Drive crummy little cars that doesn't work so instead let's build a car that people want to drive let's build a car this is hot and desirable and beautiful and convince people that driving you know electric car is not a compromise its idea of actually going in and putting it in a high-end car and breaking the mold of what an electric car wars was excitingthis is no longer gonna be a golf cart this is gonna be a Ferrari any cops watching when we first saw Tesla it had a good explanation for how they get to market without having to spend exorbitant amount of money how they would create a brand and the object of desire and consumers and it's part of the story clicked together he talked about Silicon Valley smarts being able to show Detroit how to do something that Detroit didn't think was possible its batteries its Drive electronics it's electric motors those are skills thatare present in Silicon Valley and our president Detroit Tesla's revolutionary technology for the Roadster started with a computer battery as the power source for the automobile JB Straubel Tesla's chief technology officer was the main designer of the electric powertrain for the first time it was possible to drive over 200 miles and have performance that was directly comparable competitive with what a gasoline car could do and Tesla was the first company to to take those principles and put that into practiceand try it California Governor Schwarzenegger showed up for the roadsters 2006 coming-out party a test of this one it's hot he bought one so did Leonardo DiCaprio and George Clooney but could must sell regular customers on his idea at that time there was very little activity in the auto industry in electric vehicles we were in the age of the large SUV so it was a little unusual to hear about this company in California that was planning to come to market with a high-end all-electric sports car we were very passionate about trying tomake sure that this car was going to just throw down the gauntlet on what the technology could do and really prove to the world that electric vehicles you know could be incredibly fast and could have incredibly long range there was kind of a sense of adventure you know doing these things for the first time and doing it in a really scrappy way you know we did some of the very first battery packs in my garage in Menlo Park before we actually were able to rent a real office the big issue for Tesla as with all electric cars has been thebatteries their cost and how long they last Eric Noble is president of car lab an automotive consulting firm that evaluates new cars and trucks American consumers are very ready for battery electric vehicles unfortunately battery electric vehicles aren't ready for American consumers the first results at Tesla seemed to support this gloomy forecast when we first started out the thought was simple and really obviously in retrospect quite naive which was to make use of some technology that we developed ourselvesbut also some technology that would license from AC propulsion put that together and create an electric sports car that would be compelling so let's see where did that fall apart it fell apart when the teen told musk that the projected cost had skyrocketed from $65,000 to 140,000 as the problems mounted musk faced a do-or-die decision at Tesla he would have to choose between investing his paypal pay off or let his new company collapse and Elon was looking at this and saying the dream is still there but oh my gosh what do I gotI got to get this under control by 2007 Tesla was running out of money fast no one wanted to step up to save the unproven automobile startup Elon Musk had to take the leap alone with make some pretty dramatic changes essentially recapitalized the business and invest about twice what we originally expected what we really expected as the outer limit basically Tesla got to this point where they only had enough money in the bank for a couple of months and there's nobody around who are willing to put more moneyin I take all of my reserve capital and invested in Tesla which was very scary because you know it would actually be quite sad to have the fruits of my labor with subduer and PayPal not amount to anything but there was no question that I would do that in my mind because tells it was too important to till I die I'm available 24/7 just to help solve issues right I call me 3:00 a.m. in of Sunday morning I don't care we had to go in and make some really hard decisions on on personnel changes and even really had to dedicate his time to the company I want I want I want names named so if someone's always on the hot seat and it's always the root cause for problems they will not be positive organization long term it's not okay to be unhappy and part of this company and if somebody can't get happy right musk and his board replaced Martin Eberhard one of the co-founders who had been running Teslaand I think we kind of really really exceeded the level that Everhart could handle and they're freaking apparent in 2007 we either were gonna have to shut the company down or you know wasn't have to take over as CEO it was actually a process of building a company as well as building a car you know a lot of people that fit in very well with a company when it was extremely small you didn't end up you know fitting in as well when it was larger Everhard didn't go quietly he sued musk for libel slander andbreach of contract I believe that I was scapegoated to take the blame for the programs that were not run well must resolve the disputes through mediation but his company was in serious financial trouble in September 2007 musk flew to Germany with a scheme to raise extra cash by forging an alliance with Daimler Mercedes it down is the company that invented the internal combustion engine car the maker of Mercedes smart and their endorsement carries great deal of weight so that was a just a very important moment he had to convince the companythat Tesla could supply battery packs for its cars they were skeptical really the key thing was to demonstrate a hardware that worked you know if they can't touch it they can't drive it it's not particularly real he pushed his team to retrofit a Daimler smart car with Tesla's electric motor but first they had to find one the Challenger converting a smart card to electric was doubly difficult because we couldn't find a smart card the small cone for sale in the United States we had to sendsomebody down to Mexico to buy a smart car bring-bring 1/2 to the US and then the smoke is really tiny so we had to fit up a motor how electronics of charger and everything in the squad car the challenge was daunting to replace the smart cars gas engine with a Tesla drivetrain and battery fitted in the tiny space under the hood and do it all in less than 4 weeks we didn't have much time at all we knew that from the beginning and we kind of prepared for almost battle and you know setup war room in the shop they worked around theclock stealing maps on the factory floor right up to the deadline the Daimler executives arrived and nut it all convinced that it made any sense to work with an American car company let alone a little tiny American car company in Silicon Valley while waiting for Daimler's decision must brought in one of the world's leading automobile designers to help create his next project a modern and sexy family sedan what she called the Model S originally with Model S I thought well let's let's have Kerry Kiska who was had a designstudio do the styling we paid him up pretty good sum of money to do that curiously enough the designs that he worked on that he came up with for us were terrible and what he didn't tell us was that he was actually working on a competing car company Tesla officials claimed that perhaps Henrik had come in to learn what Tesla was doing well all along he was planning to form his own company do his own vehicle we were pretty upset with him for basically taking what we're at the time the original specifications forthe Model S and then going and shopping a business plan to create that same car Tesla sued him he sued Tesla and there was all kinds of you know infighting Henrik Fisker wouldn't grant Bloomberg an interview but he told us that I believe there is enough space in the market for several new car companies that pursue a new type of electrified powertrain with different philosophies in November 2008 the court ruled in Fiskars favor it was a setback for musk who was also going through a tough time personally after eight years of marriageand five children Elon and Justine musk divorced I got divorced personal life in somewhat of a shambles and in addition getting you know attacked by some in the media my ex-wife every bad thing you could imagine there was more bad news when his bold space transport company SpaceX again failed to get a rocket in support the First Lord didn't get very far got about a minute up and then it was that there was an engine fire and that was it the second flight actually did make it to space but not too overt and then also flight 3 wedidn't get all the way to orbit he started saying I've got enough money for three cracks at it he put a hundred million dollars of his own money and he sort of hinted that the idea that after three he didn't make it it would be over SpaceX would would die must burn through the 100 million he had sunk into SpaceX now he was on his way back to the drawing board three days after the failure he announced like first that he knew what was wrong he announced that they raised money to finance a fourth and the fourth launch was gonna happenin a matter of months which in the rocket industry was a crazy announcement we were able to sell the palms and then just as we'd solve those problems where you ran smack into the the worst economic recession since the Great Depression it's been one of the darkest days on Wall Street and recent memory stock markets falling the most since 9/11 the Dow off more than 500 points this is what financial Armageddon looks like red screams that scream sell sell sell it was a week that shook Wall Street and indeed the world and a realization thatthe economy may still head into a deeper downturn as 2008 drew to a close Elon Musk faced the worst crisis of his career all three of his companies appear to be in freefall the worst point was probably just the weekend before Christmas in 2008 we had the economic tsunami take place and made things even worse if it wasn't needed we had to shut it down and and just figure out what's how do we get through this the stock period and not go bankrupt General Motors shares falling to more than a 20-year low after Goldman cut theautomakers rating to sell on a worsening sales outlook that was tough it was obviously an economic period that swore the bankruptcy of General Motors and Chrysler and there we were a young company selling a very very optional car I mean it was really you'd people don't need $100,000 sports car and they certainly wouldn't want one getting poor reviews the popular BBC programme Top Gear took the Tesla Roadster on a test drive in December 2008 this car then really was shaping up to be something wonderful[Music] although Tesla say it'll do 200 miles we worked out that on our track it would run out after just 55 miles and if it does run out it's not a quick job to charge it up again the combative CEO charged the incident was faked and said he had proof that the Roadster had not run out of power he sued the BBC to block reruns of the show the case was later dismissed but things would get even tougher his energy company Solar City founder the bank that had backed their leases pulled out of the deal I certainly did not anticipate that wewould have the worst economic climate since the Great Depression and and one which was disproportionately bad for cars I mean General Motors went bankrupt I mean general general effing motors you know musk was in the fight of his life we had maybe about a week's worth of cash in the bank all or less and there was just very little time left in the year to resolve these these things I mean they were like two or three business days left in the year I never thought I was hot it was possible for me to have a nervousbreakdown but if it was possible for me to have a nice break down there that was about as good as that's going to come when Eden was going through his sad period I was so sad I felt like I had a hole in my heart and there's nothing you can do you just hurt so much and I just didn't see him getting out of it he was just so sad and then the next thing I get this call saying wow you have made a wonderful woman the one bright spot was meeting Talulah Riley a British actress who had never heard of Tesla SpaceX orElon Musk they married in 2010 I could be some sort of hapless engineer that had wandered into a London club and he just looked so forlorn he was just sat in the corner on his Blackberry cific he was really out of place and sad I was you know trying to be very sweet to him and instead of humoring him going oh yes when he was gay this is my rocket and this is my Musk's personal life was looking up and the future of SpaceX was finally taking off the fourth attempt to launch the Falcon 1 was a huge successand three months later NASA rewarded SpaceX with a 1.6 billion dollar contract to resupply the International Space Station but musk had no time to celebrate Tesla was on the verge of financial disaster I had to make a choice then that either took all of the capital that I had left from the sale of PayPal to eBay and invest that in Tesla or Tesla would die the company is really teetering on the brink of failure and there's this board meeting late in 2008 where they're discussing what's gonna happen and Elon just says well I'm gonnaraise a 40 million dollar round to keep the company going and the board members are kind of wondering well how is he gonna do that and he says I'm gonna put it all in myself and that incredible braggadocio confidence catalyzed a change in people's opinion and we and everyone else around the table is like oh my gosh we want to be part of this want to get as much of this investment as we can he saved the company in its darkest hour with an act of heroism that is hard to describe there's nothing quite likespending your last remaining dollar on a project you believe in it was thankfully they could a good week but it definitely took its toll from the mental strain standpoint handicapped man play just burned out a few circuits just after his emergency cash infusion came the news they desperately needed a 40 million dollar deal with Daimler for smart car batteries Daimler later added 50 million for 10% of the company determined not to repeat past mistakes musk focused on bringing his family car to life so I said look we really we need to have ourown design studio and that's when I hired fronts from the host housing to design the Model S his green agenda was irresistible to van Holt thousand a legendary figure in car design who had already revolutionized the looks of the wgm and Mazda he's completely passionate to really rid the world of this addiction to fossil fuel and that that was something that he talked about from the very first sentence first conversation that we had with a new team in place must completely revamp the look of the Model S a sedan doesn't have tobe a brick doesn't have to be a big blocky car we wanted to bring this kind of passion and feeling back to this marketplace the architecture of monoliths is really similar to escape word the floor of the vehicle is the battery pack and the motors between the rear wheels and everything above that is the opportunity's base in March 2009 musk unveiled the prototype for the Model S it could hold seven passengers and as much luggage as a station wagon but to build it he knew he needed a piece of the US government's new 7.5 billiondollar loan program to support alternative energy vehicles in order for Model S to truly be successful you know it was important that the the loan come through the government funding was controversial the New York Times writer Randy Stross called the program the bailout of very very high net worth individuals who invested in Tesla Motors act musk struck back Randy's cross is a huge douche bag and an idiot okay it wasn't a bailout but alone the Obama administration agreed to lend Tesla four hundred and sixty fivemillion dollars to mass-produced the Model S a move that astounded many in the industry it's very unlikely that the Tesla investment has ever repaid to the taxpayers electric vehicles are really not possible in ways that would be effective for most consumers still this is just the religion of electric vehicles and like Jonestown that religion will come to an end there are most certain people who want to see Tesla fail because it is an attack on the mainstream car industry of course the biggest impact that testwill have is not the cause that we make ourselves but the fact that we show that you can make compelling electric cars that people really want to buy the government loan came with a challenging condition to get the money he had to first find a place to build his electric car Tesla burned through 300 million dollars since 2003 Elon Musk needed to get his model s into production fast ever the risk-taker he took another giant gamble purchasing a plant in Fremont California abandoned by Toyota The Dream Factory location where Tesla was always the newme factory which was a 50-percent Toyota factory sent General Motors factory it's one of the biggest car plants in the world it's a great location close to Tesla headquarters the reality is for very little money Toyota got an albatraoz off its books from an industry perspective it looked incredibly savvy on Toyotas part and incredibly naive on Tesla's part car factories are big pieces of sunk capital to retool a factory it takes a tremendous incremental investment the acquisition released the government funds to beginproduction despite the fact that Tesla had posted a profit just once since its founding musk took his company public in June 2010 the smartest money in the world is is betting on Tesla not everyone was as upbeat about the company's future Tesla stock voted by Wall Street as the least likely to succeed you don't want on this stock you don't want you shouldn't even write the darn thing there hasn't been an IPO of a car company in America since Henry Ford and that caught people's attention investorsignored the skeptics Tesla raised 226 million dollars in its IPO must now had the capital to get rolling on the Model S you just saw on his face this sort of just relation and this feeling like all of this suffering is worth it and it's anger and it's real now in 2010 after winning the 1.6 billion dollar contract from NASA SpaceX became the first private company to successfully launch and return a spacecraft from orbit so SpaceX was the first purely commercial ground-up development to reach orbit the first successful launch at SpaceX was I'm just wishing through any entities that are listening please bless this launch and two years later in May 2012 it made history as the first privately held company to send a cargo payload to the International Space Station back on earth the long-awaited launch of Tesla's new sedan was also taking offits time delivery model s the Model S started rolling off the production line although questions about range and service remained not everyone was cheering in a 2012 presidential debate Republican candidate Mitt Romney blasted President Obama for the government loan to Tesla lumping Tesla in with other financially troubled green companies you put 90 billion dollars like 50 years worth of breaks into into solar and wind to suck it Solyndra and Fisker and Tesla and enter one I mean I add a friend who said you don't just pick the winners andlosers he picked the losers but Tesla was no loser in the eyes of the automotive industry the Model S was the first electric sedan to win motor trends Car of the Year musk didn't have much time to celebrate a few months later the New York Times delivered a devastating review of the Model S it reported the battery died on its test drive from Washington to Boston and published an image no CEO would want there was a sad shot of about car owner on a flatbed as though that was the only outcome possible for for such a driveand that's just that's just not true musk went on the offensive unless people said oh you know should it doesn't matter if you're right or wrong you don't battle the New York Times and it's like the hell with that the battle between the reporter and the renegade CEO ended when the New York Times public editor concluded the reporting was imprecise but not done in bad faith but the story didn't affect his bottom line remember that loser comment from a presidential candidate about the 465million dollar government loan it really feels good to have have repaid the US taxpayer that's that's really what's important here and and we're not we didn't just repay the principal we actually repaid it with interest and and a bonus pay and so ultimately the the US taxpayer actually made a profit of over 20 million dollars on this Tesla repaid the loan nine years ahead of schedule never short on optimism or confidence musk made a stunning promise for the nearly $70,000 car we're guaranteeing that the value of the ModelS will be no less than that of a Mercedes s-class after three years I am personally guaranteeing that value and standing behind that guarantee with all of my assets not just with with Tesla he has guaranteed free charging for the life of the car and has expanded the charging system across the country you'll be able to travel all the way from LA to New York just using the Tesla supercharger network and supercharger system is free so it's not just free now it's get free forever that's the Teslacommitment his commitment to customers has paid off since its IPO Tesla shares were up more than fivefold SpaceX and Solar City were also turning profits and this is a biggest and most important customer but almost three quarters of our customers are commercial SpaceX says it has more than four billion dollars in revenue under contract but of all his companies perhaps the greatest success was the one addressing the world's energy need Solar City is now the largest solar service provider in the US and has more than quadrupled in valuesince its initial public offering in December 2012 Solar City has been very very impressive I mean there are you know thousands of people with panels on their roofs and and lots of big offices I believe eBay has solar city panels so it's it's having a very big very visible impact on the world he really wants to change the world and in my vision of the future that you'll have clean and renewable sources of energy feeding the grid and our all of our vehicles will run off that this is really the future it's somethingwonderful stories about Iran has a self-confidence that is just it's breathtaking and it's especially breathtaking when you think about the things he's confident about the idea that Humanity is gonna get to Mars that not just humanity is gonna get to Mars that but that he in his lifetime Elon Musk will get to Mars Crusader or canny businessman named one of Time Magazine's most influential people in the world the risk-taking multitasking CEOs estimated net worth was six billion dollars in June 2013 divorced for the second timemusk splits his time between his five sons his companies and thinking about the future just is it significant it really is question is all things I'm working on are they really gonna matter or do they have the potential for really matter [Music]

- The following is a conversation with Elon Musk, his third time on this, the "Lex Fridman Podcast." Yeah, make yourself comfortable. - Boo. - Oh, wow, okay. - You don't do the headphone thing? - No. - Okay. I mean, how close do I need to get this thing? - The closer you are the sexier you sound.- Hey babe, sup. - Yup. - Can't get enough of you on that baby? (both laughing) - I'm gonna clip that out and any time somebody messages me on my phone I'll just respond with that. - If you want my body and you think I'm sexy come right out and tell me so. Do do do do do. - [Shivon] So funny.- So good. Okay, serious mode activate, alright. - Serious mode. Come on, your Russian, you can be serious. - Yeah I know. - Everyone's serious all the time in Russia. - Yeah, yeah. We'll get there. We'll get there. (Shivon speaking faintly) Just gotten soft. Allow me to say that the SpaceX launch of human beings to orbit on May 30th, 2020, was seen by many as the first step in a new era of human space exploration.These human space flight missions were a beacon of hope to me and to millions over the past two years as our world has been going through one of the most difficult periods in recent human history. We see the rise of division, fear, cynicism, and the loss of common humanity, right when it is needed most. So, first, Elon, let me say thank you for giving the world hope and reason to be excited about the future.- Oh, it's kind of you to say that. I do want to do that. Humanity has, obviously a lot of issues, and people at times do bad things, but despite all that, I love humanity and I think we should make sure we do everything we can to have a good future and an exciting future, and one where that maximizes the happiness of the people.- Let me ask about a Crew Dragon Demo-2. So that first flight with humans onboard, how did you feel leading up to that launch? Were you scared? Were you excited? What was goin' through your mind? So much was at stake. - Yeah, no, that was extremely stressful. The question we obviously could not let them down in any way.So, extremely stressful I'd say, to say the least. I was confident that, at the time that we launched, that no one could think of anything, at all, to do that would improve the probability of success and we racked our brains to think of any possible way to improve the probability of success, and we could not think of anything more, nor could NASA, and so, that's just the best that we could do.So then we went ahead and launched. Now, I'm not a religious person, but I nonetheless got on my knees and prayed for that mission. - [Lex] Were you able to sleep? - No. - How did it feel when it was a success? First when the launch was a success, and when they returned back home, or back to earth. - It was a great relief.Yeah. For high stress situations I find it's not so much elation, as relief. And, I think once as we got more comfortable and proved out the systems, 'cause we really, you're gotta make sure everything works. It was definitely a lot more enjoyable with the subsequent asteroid missions. And I thought the Inspiration mission was actually very inspiring, the Inspiration4 mission.I'd encourage people to watch the Inspiration documentary on Netflix, it's actually really good. And it really isn't, I was actually inspired by that, so that one I felt, I was kind of able to enjoy the actual mission and not just be super stressed all the time. - So, for people that somehow don't know, it's the all civilian, first time all civilian out to space out to orbit.- Yeah, it was the, I think the highest obit that in like, I don't know, 30 or 40 years or something, the only one that was higher was the one shuttle, sorry, a Hubble servicing mission. And then before that it would've been Apollo in '72. It was pretty wild. So it's cool. It's good. I think as a species, we want to be continuing to do better and reach higher ground.I think it would be tragic, extremely tragic, if Apollo was the high watermark for humanity, and that that's as far as we ever got. And it's concerning that here we are 49 years after the last mission to the moon. And, so almost half a century, and we've not been back. And that's worrying, it's like, does that mean we've peaked as a civilization or what? I think we gotta get back to the moon and build a base there.A science base. I think we could learn a lot about the nature of the universe if we have a proper science base on the moon. We have a science base in Antarctica and many other parts of the world. So that's what I think the next big thing we've gotta have like a serious black moon base, and then get people to Mars and get out there and be a space bearing civilization.- I'll ask you about some of those details. But, since you're so busy with the hard engineering challenges of everything that's involved, are you still able to marvel at the magic of it all, of space travel, of every time the rocket goes up, especially when it's a crude mission? Or are you just so overwhelmed with all the challenges that you have to solve? And actually, sort of to add to that, the reason I wanted to ask this question of May 30th, it's been some time, so you can look back and think about the impact already.At the time it was an engineering problem maybe, now it's becoming a historic moment. Like it's a moment that, how many moments will be remembered about the 21st century? To me, that or something like that, maybe Inspiration4 or one of those will be remembered as the early steps of a new age of space exploration.- Yeah, I mean, during the launches itself, so I mean, I think maybe some people will know, but a lot of people don't know, is I'm actually the chief engineer of SpaceX, so I've signed off on pretty much all the design decisions. So if there's something that goes wrong with that vehicle, it's fundamentally my fault, you know? So I'm really just thinking about all the things that like, so when I see the rocket, I see all the things that could go wrong, and the things that could be better,and the same with the Dragon spacecraft. Other people will say, "Oh, this is a spacecraft or a rocket." and "This looks really cool." I'm like, I've like a readout of these are the risks, these are the problems. That's what I see. Like (Elon chuffing) So it's not what other people see when they see the product.- So let me ask you then to analyze Starship in that same way. I know you have, you'll talk a bit in more detail about Starship in the near future. Perhaps you had that- - We can talk about in now if you want. - But, just in that same way, like you said, you see, when you see a rocket, you see the sort of a list of risks.In that same way, you said that Starship was a really hard problem. So, there's many ways I can ask this, but if you magically could solve one problem perfectly, one engineering problem perfectly, which one would it be? - [Elon] On Starship? - On, sorry, on Starship. So is it maybe related to the efficiency, the engine, the weight of the different components, the complexity of various things, maybe the controls of the crazy thing it has to do to land? - No, it's actually, by far the biggest thing of solving my timeis engine production. Not the design of the engine, I've often said prototypes are easy. Production is hard. So, we have the most advanced rocket engine that's ever been designed. 'Cause I say currently the best rocket engine ever is probably the RD-180 or RD-170 the dual Russian engine, basically.And still, I think an engine should only count if it's gotten something to orbit. And so our engine has not gotten anything to orbit yet, but it is, it's the first engine that's actually better than the Russian RD engines, which were amazing design. - So you're talking about Raptor engine. What makes it amazing? What are the different aspects of it that make it, what are you the most excited about if the whole thing works in terms of efficiency, all those kinds of things? - Well, it's, the Raptor isa full flow staged combustion engine, and it's operating at a very high TAVR pressure. So, one of the key figures, merit, perhaps the key figure of merit is what is the chamber pressure at which the rocket engine can operate? That's the combustion chamber pressure. So a Raptor is designed to operate at a 300 bar, possibly, maybe higher, than standard atmospheres.The record right now for operational engine is the RD engine that I mentioned, the Russian RD, which is, I believe around 267 bar. And the difficulty of the chamber pressure is increases on a non-linear basis. So, 10% more TAVR pressure is more like 50% more difficult, but that air pressure, that is what allows you to get a very high power density for the engine.So, enabling a very high thrust to weight ratio and a very high, specific impulse. So, specific impulse is like a measure of the efficiency of a rocket engine. It's really the exhaust, the effect of exhaust velocity of the gas coming out of the engine. With a very high chamber pressure you can have a compact engine that nonetheless has a high expansion ratio, which is the ratio between the exit nozzle and the throat.You see a rocket engine has got sort of like a hourglass shape. It's like a chamber and then it necks down and there's a nozzle, and the ratio of the exit diameter to the throat expansion ratio. - So why is this such a hard engine to manufacture at scale? - It's very complex. - What does complexity mean? Here's a lot of components involved.- There's a lot of components and a lot of unique materials. So we had to invent several alloys that don't exist in order to make this engine work. - So it's a materials problem too. - It's a materials problem, and in a stage combustion, that full floor stage combustion, there are many feedback loops in the system.Basically you've got propellants and hot gas flowing simultaneously to so many different places on the engine. And they all have a recursive effect on each other. So you change one thing here, it has a recursive effect here. It changes something over there. And it's quite hard to control. There's a reason no one's made this before.And the reason we're doing a stage commotion full flow is because it has the highest theoretical possible efficiency. So in order to make a fully reasonable rocket, which, that's really the holy grail of orbital rocketry, you have to have, everything's gotta be the best. It's gotta be the best engine, the best airframe, the best heat shield, extremely light avionics, very clever control mechanisms.You've got to shed mass in any possible way that you can. For example, we are, instead of putting landing legs on the booster and ship, we are going to catch them with a tower to save the weight of the landing legs. So that's like, I mean, we're talking about catching the largest flying object ever made on a giant tower with chopstick arms.It's like "Karate Kid" with the fly, but much bigger. (Elon laughing) - I mean, pulling something- - This probably won't work the first time. (Elon laughing) So this is bananas. This is bananas stuff. - So you mentioned that you doubt, well, not you doubt, but there's days or moments when you doubt that this is even possible.It's so difficult. - The possible part is, well at this point, we'll I think we'll get Starship to work. There's a question of timing. How long will it take us to do this? How long will it take us to actually achieve full and rapid reusability? 'Cause it will probably many launches before we are able to have full and rapid reusability.But I can say that the physics pencils out, we're not, at this point I'd say we're confident that, let's say, I'm very confident success is in the set of all possible outcomes. - [Lex] Mm, right, it's not in all set of. - For a while there I was not convinced that success was in the set of possible outcomes.(Lex laughing) Which is very important actually. But, so... - [Lex] So you're saying there's a chance. - I'm saying there's a chance. Exactly. Just not sure how long it will take. But we have a very talented team, they're working night and day to make it happen. Like I said, the critical thing to achieve with revolution in space flight and for humanity to be a space bearing civilization is to have a fully and rapidly reusable rocket, orbital rocket.There's not even been any orbital rocket that's been fully reusable ever. And this has always been the holy grail of rocketry and many smart people, very smart people, have tried to do this before, and they've not succeeded. 'Cause it's such a hard problem. - What's your source of belief in situations like this when the engineering problem is so difficult, there's a lot of experts, many of whom you admire, who have failed in the past.- [Elon] Yes. - A lot of people, a lot of experts, maybe journalists, all the kinds of, the public in general, have a lot of doubt about whether it's possible, and you yourself know that even if it's a non-nodal set, not empty set, of success, it's still unlikely or very difficult. Where do you go to both personally, intellectually as an engineer, as a team, for source of strength needed to sort of persevere through this and to keep going with the project, take it to completion? - I suppose the strength. Hmm.That's really not how I think about things. I mean, for me, it's simply this is something that is important to get done and we should just keep doing it or die trying, and I don't need a source of strength. - So quitting is not even like... - It's not, it's not in my nature. - Okay. - And I don't care about optimism or pessimism.Fuck that, we're gonna get it done. - [Lex] Gonna get it done. Can you then zoom back in to specific problems with Starship or any engineering problems you work on? Can you try to introspect your particular biological neural network, your thinking process, and describe how you think through problems, the different engineering and design problems? Is there like a systematic process you've spoken about, first principles thinking, but is there kind of - Yeah, absolutely.- process to it? - Saying like, physics is low and everything else was a recommendation. I've met a lot of people that can break the law, but I have never met anyone who could break physics. So first for any kind of technology problem you have to sort of just make sure you're not violating physics. First principles analysis, I think, is something that can be applied to really any walk of life, anything really.It's really just saying, let's boil something down to the most fundamental principles, the things that we are most confident are true at a foundational level, and that sets your axiomatic base, and then you reason up from there. And then you cross check your conclusion against the axiomatic truth. Some basics in physics would be like are violating conservation of energy or momentum or something like that, then it's not gonna work.So that's just to establish is it possible? And then another good physics tool is thinking about things in the limit. If you take a particular thing and you scale it to a very large number or to a very small number, how do things change? - Both in number of things you manufacture, something like that, and then in time.- Yeah, let's say, take an example of manufacturing, which I think is just a very underrated problem. Like I said, it's much harder to take an advanced technology part and bring it into volume manufacturing, than it is to design it in the first place. More is magnitude. So let's say you're trying to figure out, why is this part or product expensive? Is it because of something fundamentally foolish that we're doing? Or is it because our volume is too low? And so then you say, okay, well what if our volume was a million units a year?Is it still expensive? That's what I'm radical, thinking about things to the limit. If it's too expensive at a million units a year, then volume is not the reason why your thing is expensive. There's something fundamental about the design. - And then you then can focus on the reducing complexity or something like that in the design.- Gotta change the design to, change the part to be something that is not fundamentally expensive. That's a common thing in rocketry 'cause the unit volume is relatively low, and so a common excuse would be "Well, it's expensive because our unit volume is low. And if we were in like automotive or something like that, or consumer electronics, then our costs would lower." I'm like, "Okay, so let's say" we skip, "now you're making a million units a year. Is it still expensive?" If the answer is yes, then economies of scale are not the issue. - Do you throw, into manufacturing, do you throw like supply chain, you talked about resources and materials and stuff like that, do you throw that into the calculation of trying to reason from first principles? Like, how are we gonna make the supply chain work here? - Yeah, yeah.- [Lex] And then the cost of materials, things like that, or is that too much? - Yeah. Exactly. Like a good example of thinking about things in the limit is if you take any product, any machine or whatever, like take a rocket or whatever, and say, if you've got, if you look at the raw materials in the rocket, so you're gonna have like aluminum, steel, titanium, Inconel, specialty alloys, copper.And you say, "What's the weight of the constituent elements of each of these elements, and what is their raw material value?" And that sets the asymptotic limit for how low the cost of the vehicle can be, unless you change the materials. And then when you do that, I call it like maybe the magic one number or something like that.So that would be like, if you had the, just a pile of these raw materials here, and you could wave a magic wand and rearrange the atoms into the final shape, that would be the lowest possible cost that you could make this thing for, unless you change the materials. So then, and that is always, almost always a very low number.So then, what's actually causing things to be expensive is how you put the atoms into the desired shape. - Yeah, actually, if you don't mind me taking a tiny tangent, I had a, I often talk to Jim Keller who's somebody that worked with you as a- - Oh yeah. Jim did great work at Tesla. - So, I suppose he carries the flame of the same kind of thinking that you're talking about now.I guess I see that same thing at Tesla and SpaceX folks who work there, they kind of learn this way of thinking and it kinda becomes obvious almost. But anyway, I had argument, not argument. He educated me about how cheap it might be to manufacture Tesla Bot. We just, we had an argument. How can you reduce the cost, of scale, of producing a robot? Because, so far I've gotten a chance to interact quite a bit, obviously in the academic circles, with humanoid robots, and then with Boston Dynamics and stuff like that.And they're very expensive to build. And then Jim kinda schooled me on saying like, "Okay, this kind of first principles thinking of how can we get the cost of manufacturing down." I suppose you do that, you have done that kind of thinking for Tesla Bot and for all kinds of, all kinds of complex, systems that are traditionally seen as complex, and you say, "Okay, how can we simplify everything down?" - Yeah.I mean, I think if you are really good at manufacturing, you can basically make, at high volume you can basically make anything for a cost that asymptotically approaches the raw material value of the constituents, plus any intellectual property that you need to license. Anything. - Right. - But it's hard.It's not like that's a very hard thing to do, but it is possible for anything. Anything in volume can be made of, like I said, for a cost that asymptotically approaches it's raw material constituents plus intellectual property license rights. So what will often happen in trying to design a product is people will start with the tools and parts and methods that they are familiar with, and try to create a product using their existing tools and methods.The other way to think about it is actually imagine the, try to imagine the platonic ideal of the perfect product or technology, whatever it might be, and say, "What is this? What is the perfect arrangement of atoms that would be the best possible product? And now let us try to figure out how to get the atoms in that shape." - I mean, it sounds, it's almost like "Rick and Morty" absurd until you start to really think about it. And you really should think about it in this way 'cause everything else is kind of, if you think you might fall victim to the momentum of the way things are done in the past, unless you think in this way.- Well, just as a function of inertia, people will want to use the same tools and methods that they are familiar with. That's what they'll do by default. - [Lex] Yeah. - And then that will lead to an outcome of things that can be made with those tools and methods, but is unlikely to be the platonic ideal of the perfect product.So that's why it's good to think of things in both directions, so like what can we build with the tools that we have, but also what is the perfect, the theoretical perfect product look like? And that theoretical perfect product is gonna be a moving target, 'cause as you learn more the definition of that perfect product will change 'cause you don't actually know what the perfect product is, but you can successfully approximate a more perfect product.So, thinking about it like that, and then saying, "Okay, now what tools, methods, materials, whatever, do we need to create in order to get the atoms in that shape? But people very rarely think about it that way. But it's a powerful tool. - I should mention that the brilliant Shivon Zilis is hanging out with us, in case you hear a voice of wisdom from outside, from up above.Okay. So let me ask you about Mars. You mentioned it would be great for science to put a base on the moon, to do some research, but the truly big leap, again, in this category of seemingly impossible, is to put a human being on Mars. When do you think SpaceX will land a human being on Mars? - Hm. Best case is about five years, worst case 10 years.- What are the determining factors, would you say, from an engineering perspective? Or is that not the bottlenecks? - No, it's fundamentally you're engineering the vehicle. I mean Starship is the most complex and advanced rocket that's ever been made by, I don't know, order of magnitude or something like that.It's a lot. It's really next level. And the fundamental optimization of Starship is minimizing cost per ton to orbit, and ultimately cost per ton to the surface of Mars. This may seem like a mercantile objective, but it is actually the thing that needs to be optimized. There is a certain cost per ton to the surface of Mars where we can afford to establish a self-sustaining city.And then above that, we cannot afford to do it. So, right now you can fly to Mars for $1 trillion. No amount of money could get you a ticket to Mars. So we need to get that above, to get that like something that is actually possible at all. We don't want to just wanna have, with Mars, flags and footprints, and then not come back for a half century like we did with the moon.In order to pass a very important, great filter. I think we need to be a multi-planet species. This ways sound somewhat esoteric to a lot of people, but, eventually given enough time, something, Earth is likely to experience some calamity, that could be something that humans do to themselves, or an external event like happened to the dinosaurs.But if, eventually, if none of that happens, and somehow, magically, we keep going, then the sun will, the sun is gradually expanding and will engulf the earth. And probably Earth gets too hot for life in about 500 million years. It's a long time, but that's only 10% longer than earth has been around.And so if you think about like the, the current situation, it's really remarkable and kind of hard to believe, but Earth's been around four and a half billion years, and this is the first time in four and a half billion years that it's been possible to extend life beyond Earth. And that window of opportunity may be open for a long time, and I hope it is, but it also may be open for a short time, and we should, I think it is wise for us to act quickly while the window is open.Just in case it closes. - Yeah, the existence of nuclear weapons, pandemics, all kinds of threats, - [Elon] Yeah. - should kind of give us some motivation. - I mean, civilization could get, could die with a bang or a whimper. If it dies of demographic collapse, then it's more of a whimper, obviously. And if it's World War III, it's more of a bang, but these are all risks.I mean, it's important to think of these things and just, things like probabilities, not certainties, there's a probability that something bad will happen on earth. I think most likely the future will be good, but there's, let's say for argument's sake, a 1% chance per century of a civilization ending event.Like that was Stephen Hawking's estimate. I think he might be right about that. We should basically think of this, being a multi-planet species, just like taking out insurance for life itself, like life insurance for life. (both laughing) - This turned into a infomercial real quick. - Life insurance for life, yes.And we can bring the creatures from, plants and animals from Earth to Mars, and breathe life into the planet, and have a second planet with life. That would be great. They can't bring themselves there, so if we don't bring them to Mars, then they will just for sure all die when the sun expands anyway, and then that'll be it.- What do you think is the most difficult aspect of building civilization on Mars, terraforming Mars, like from engineering perspective, from a financial perspective, human perspective, to get a large number of folks there who will never return back to Earth? - No, they could certainly return, some will return back to Earth.- They will choose to stay there for the rest of their lives. - Yeah, many will. We need the spaceships back, like the ones that go to Mars, we need them back, so you can hop on if you want. But we can't just not have the spaceships come back, those things are expensive. We need them back. I'd like to come back and journal their trip.- I mean, do you think about the terraforming aspect, actually building, are you're so focused right now on the spaceships part that's so critical to get to Mars? - Yeah, yeah. We absolutely, if you can't get there, nothing else matters. And like I said, we can't get there at some extraordinarily high cost.I mean, the current cost of let's say one ton to the surface of Mars is on the order of a billion dollars. So, 'cause you don't just need the rocket and the launch and everything, you need like heat shield, you need guidance system, you need deep space communications. You need some kind of landing system.So, like rough approximation would be a billion dollars per ton to the surface of Mars right now. This is obviously way too expensive to create a self-sustaining civilization. So we need to improve that by at least a factor of a thousand. - [Lex] A million per ton? - Yes, ideally less than, much less than a million ton.You have to say like, well how much can society afford to spend or want to spend on a self-sustaining city on Mars? The self-sustaining part is important. Like it's just the key threshold, the grateful to, we'll have been passed, when the city on Mars can survive even if the space ships from earth stop coming, for any reason.Doesn't matter what the reason is. But if they stop coming for any reason, will it die out or will it not? And if there's even one critical ingredient missing, then it still doesn't count. It's like if you're in a long sea voyage and you've got everything except vitamin C. (Elon laughing) It's only a matter of time, you're gonna die.So we gotta get a Mars city to the point where it's self sustaining. I'm not sure this will really happen in my lifetime, but I hope to see it at least have a lot of momentum. And then you could say, "Okay, what is the minimum tonnage necessary to have a self-sustaining city?" And there's a lot of uncertainty about this.You could say, I dunno, it's probably at least a million tons. 'Cause you have to set up a lot of infrastructure on Mars. Like I said, you can't be missing anything that in order to be self-sustaining, you can't be, like you need a semiconductor, fabs, you need iron ore refineries, you need lots of things, you know? And Mars is not super hospitable.It's the least inhospitable planet, but it's definitely a fixer upper of a planet. - [Lex] Outside of Earth. - Yes. - Earth is pretty good. - Earth is like easy. Yeah. - And, also, we should clarify in the solar system. - [Elon] Yes. In the solar system. - There might be nice like vacation spots. - There might be some great planets out there, but it's hopeless- - Too hard to get there? - Yeah, way, way, way, way, way too hard, to say the least.- Let me push back on that. Not really a pushback, but quick a curve ball of a question. So you did mention physics as the first starting point. General relativity allows for worm holes. They technically can exist. Do you think those can ever be leveraged by humans to travel fast in the speed of light? Or are you saying- - The worm hole thing is debatable.We currently do not know of any means of going faster than the speed of light. There are some ideas about having space. You're gonna move at the speed of light through space, but if you can make space itself move, that would be warping space. Space is capable of moving faster than the speed of light. - [Lex] Right.- Like the universe in the big bang, the universe expanded at much more than the speed of light, by a lot. - [Lex] Yeah. If this is possible, the amount of energy required to warp space is so gigantic, it boggles the mind. - So, all the work you've done with propulsion, how much innovation is possible with rocket propulsion? I mean, you've seen it all, and you're constantly innovating in every aspect.How much is possible? Like how much, can you get 10 X somehow? Is there something in there, in physics, that you can get significant improvement in terms of efficiency of engines and all those kinds of things? - Well, as I was saying, really the holy grail is a fully and rapidly reasonable orbital system. Right now, the Falcon 9 is the only reusable rocket out there.The booster comes back and lands, you've seen the videos. And we got the nose cone or fairing back, but we do not get the upper stage back. That means that we have a minimum cost of building an upper stage. You can think of like a two-stage rocket of sort of like two airplanes, like a big airplane and a small airplane, and we get the big airplane back, but not the smaller airplane.And so it still costs a lot. That upper stage is at least $10 million. And then the degree of the booster is not as rapidly and completely reusable as we'd like in order of the pharynx. So, our kind of minimum marginal cost not counting overhead for per flight is on the order of 15 to $20 million, maybe.That's extremely good for, it's by far better than any rocket ever in history. But with full and rapid reusability, we can reduce the cost per ton to orbit by a factor of a hundred. Just think of it like, like imagining if you had an aircraft or something or a car. And if you had to buy a new car every time you went for a drive, that'll be very expensive.It'll silly, frankly. - Mhm. - But, in fact, you just refuel the car or recharge the car and that's makes your trip, I don't know, a thousand times cheaper. So, it's the same for rockets. Very difficult to make this complex machine that can go to orbit. And so if you cannot reuse it, and have to throw even any significant part of it away, that massively increases the cost.Starship in theory could do a cost per launch of like a million, maybe $2 million or something like that. And put over a hundred tons in orbit, which is crazy. - Yeah. That's incredible. So you're saying it's, by far the biggest bang for the buck is to make it fully reusable versus like some kind of brilliant breakthrough in theoretical physics.- No, no, there's no, there's no brilliant brea, no, there's no. We gotta make the rocket reusable, this is an extremely difficult engineering problem. - Got it. - But no new physics is required. - Just brilliant engineering. Let me ask a slightly philosophical fun question. Gotta ask. I know you're focused on getting to Mars, but once we're there on Mars, what form of government, economic system, political system, do you think would best for an early civilization of humans? The interesting reason to talk about this stuff,it also helps people dream about the future. I know you're really focused about the short-term engineering dream, but it's like, I don't know. There's something about imagining an actual civilization on Mars that gives people, - Sure. - really gives people hope. - Well, it would be a new frontier and an opportunity to rethink the whole nature of government just as was done in the creation of the United States.I mean, I would suggest having a direct democracy, like people vote directly on things, as opposed to representative democracy. So, representative democracy, I think, is too subject to a special interests and coercion of the politicians and that kind of thing. So I'd recommend that there's just direct democracy.People vote on laws, the population votes on laws themselves, and then the laws must be short enough that people can understand them. - Yeah, and then keeping a well-informed populace, really being transparent about all the information about what they're voting for. - Yeah. Absolute transparency. - Yeah.And not make it as annoying as those cookies we have to accept- - Have to accept cookies. There's always a slight amount of trepidation when you click accept cookies. I feel as though there's perhaps a very tiny chance that'll open a portal to hell or something like that. - [Lex] That's exactly how I feel.Why do they keep wanting me to accept that? What do they want with this cookie? Somebody got upset with accepting cookies or something somewhere. I mean, who cares? So annoying to keep accepting all these cookies. - [Lex] To me, it's just a great- - I'm tired of accept- (Shivon speaking faintly) Yes you can have my damn cookie, I don't care.Whatever. - [Lex] You heard it from me Elon first, he accepts all your damn cookies. - Yeah. (both laughing) And stop asking me. It's annoying. - Yeah, it's one example of implementation of a good idea done really horribly. - Yeah, somebody was like, there's some good intentions of like privacy or whatever, but now everyone's just has to tick accept cookies and it's now, you have billions of people who have to keep clicking accept cookie and it's super annoying.Just accept the damn cookie, it's fine. There is like, I think fundamental problem that we're, because we've not really had a major, like a world war or something like that in a while. And obviously we would like to not have world wars. There's not been a cleansing function for rules and regulations.So wars did have some silver lining in that there would be a reset on rules and regulations after a war. So World Wars I and II there were huge resets on rules and regulations. If society does not have a war, and there's no cleansing function or garbage collection for rules and regulations, then rules and regulations will accumulate every year 'cause they're immortal.There's no actual, humans die, but the laws don't. So, we need a garbage collection function for rules and regulations that should not just be immortal. 'Cause some of the rules and regulations that are put in place will be counterproductive, done with good intentions, but counterproductive. And sometimes not done with good intentions.If rules and regulations just accumulate every year, and you get more and more of them, then eventually you won't be able to do anything. You're just like Gulliver with, tied down by thousands of little strings. And we see that in, U.S. and LA, basically all economies that have been around for awhile, and regulators and legislators create new rules and regulations every year, but they don't put effort into removing them.And I think that's very important that we put effort into removing rules and regulations. But it gets tough 'cause you get special interests that then are dependent on, they have a vested interest in that whatever rule regulation and that they, then they fight to not get it removed. - Yeah. I mean, I guess the problem with the constitution is it's kinda like C versus Java 'cause it doesn't have any garbage collection built in.I think there should be. When you first said the metaphor of garbage collection, I loved it - Yeah, it's from a coding standpoint. - From a coding standpoint, yeah, yeah. It would be interesting if the laws themselves kinda had a built in thing where they kinda die after a while, unless somebody explicitly publicly defends them.So that's sort of, it's not like somebody has to kill them. They kinda die themselves. They disappear. - [Elon] Yeah. - Not to defend Java or anything, C++, you could also have great garbage collection in Python and so on. - Yeah. So, yeah, something needs to happen or just the civilizations arteries just harden over time.And you can just get less and less done because there's just a rule against everything. So I think, I don't know, for Mars, or whatever, I say, or even for here, obviously for Earth as well, I think there should be an active process for removing rules and regulations and questioning their existence.If we've got a function for creating rules and regulations, 'cause rules and regulations could also think of as like, they're like soft work or lines of code for operating a civilization, that's the rules and regulations. So it's not like we shouldn't have rules and regulations, but you have your code accumulation, but no code removal.And so it just gets to be become basically archaic bloatware after a while. And it's just, it makes it hard for things to progress. So, I don't know, maybe Mars you'd have like any given law must have a sunset, and require active voting to keep it up there. I actually also say like, and these are just, I don't know, recommendations or thoughts, and ultimately will be up to the people on Mars to decide, but I think it should be easier to remove a law than to add one, because of the, just to overcome the inertia of laws.So, maybe it's like, for argument's sake, you need like say 60% vote to have a law take effect, but only a 40% vote to remove it. - So let me be the guy, you posted a meme on Twitter recently where there's like a row of urinals and a guy just walks all the way across - So true, yeah. - and he tells you about crypto. - Listen, I mean, that's happened to me so many times, I think maybe even literally. (both laughing) - Do you think technologically speaking there's any room for ideas of smart contracts or so on? 'Cause you mentioned laws, that's an interesting implement use of things like smart contracts to implement the laws by which governments function.Like something built on Ethereum, or maybe a dog coin that enables smart contracts somehow. - I never, I didn't quite understand this whole smart contract thing. (both laughing) I'm too downtown to understand smart contracts. - That's a good line. (both laughing) - I mean, my general approach to any kind of deal or whatever is just make sure there's clarity of understanding.That's the most important thing. - [Lex] Right. - And just keep any kind of deal very short and simple, plain language, and just make sure everyone understands this is the deal. Does everyone, is it clear? And what are the consequences if first things don't happen? But usually deals are, business deals or whatever are way too long and complex and overly lawyered and pointlessly.- You mentioned that Doge is the people's coin. - [Elon] Yeah. - And you said that you were literally going, SpaceX may consider literally putting a Dogecoin on the moon. - Yeah. - Is this something you're still considering, Mars perhaps, do you think there's some chance, we've talked about political systems on Mars, that a Dogecoin is the official currency of Mars, it's the coin of the future? - Well, I think Mars itself will need to have a different currency because you can't synchronize due to speed of light,or not easily. - So it must be complete standalone from earth. - Well, yeah, Mars is, at closest approach, it's four light minutes away roughly, and then add for this approach, it's roughly 20 light minutes away, maybe a little more. So you can't really have something synchronizing if you've got a 20 minute speed of light issue, if it's got a one minute blockchain.It's not gonna synchronize properly. I don't know if Mars would have a cryptocurrency as a thing, but probably, seems likely. But it would be so kind of localized thing on Mars. - And you let the people decide. - Yeah, absolutely. The future of Mars should be up to the martians. I mean, I think the cryptocurrency thing is an interesting approach to reducing the error in the database that is called money.I think I have a pretty deep understanding of what money actually is on a practical day-to-day basis, because of PayPal. We really got in deep there. And right now the money system, actually for practical purposes is really a bunch of heterogeneous mainframes running a old COBOL. - [Lex] Okay, you mean literally- - Literally. - That is literally what's happening. - in batch mode. Okay. - In batch mode. - Yeah. Pity the poor bastards who have to've maintained that code. Okay. That's pain. - [Lex] Not even Fortrans, COBOL, yep. - That's COBOL. And they still, the banks are still buying mainframes, in 2021, and running engine COBOL code. The federal reserve is like probably even older than what the banks have, and they have an old COBOL mainframe.And so the government effectively has editing privileges on the money database. And they use those editing privileges to make more money whenever they want. And this increases the error in the database that is money. So I think money should really be viewed through the lens of information theory. You're kinda like an internet connection.Like what's the bandwidth, total bit rate, what is the latency jitter, packet drop, errors in the network communication. Just think of money like that basically. I think that's probably what I really think of it. And then say what system, from an information theory standpoint, allows an economy to function the best.Crypto is an attempt to reduce the error in money that is contributed by governments diluting the money supply as basically a pernicious form of taxation. - So both policy in terms of with inflation, and actual like technological, COBOL, cryptocurrency takes us into the 21st century in terms of the actual systems that allow you to do the transaction, to store wealth, all those kinds of things.- Like I said, just think - In theory. - of money as information, people often will think of money as having power in and of itself. It does not. Money is information, and it does not have power in and of itself. Applying the physics tools of thinking about things in the limit is helpful. If you are stranded on a tropical island and you have a trillion dollars, it's useless.'Cause there's no resource allocation. Money is a database of resource allocation, but there's no resources to allocate except yourself. So money's useless. If you're stranded on a desert island with no food, all the Bitcoin in the world will not stop you from starving. - [Lex] Yeah. - Just think of money as a database for resource allocation across time and space.And then what system, in what form should that database, or data system, what would be most effective? There is a fundamental issue with, say Bitcoin, in its current form in that it's, the transaction volume is very limited. And the latency, the latency, for a properly confirmed transaction is too long, much longer than you'd like.It's actually not great from transaction volume standpoint or latency standpoint. So it is perhaps useful as, to solve an aspect of the money database problem, which is the sort of store of wealth or an accounting of relative obligations, I suppose. But it is not useful as a currency, as a day-to-day currency.- But people have proposed different technological solutions- - [Elon] Like Lightning. - Yeah, Lightening Network and the Layer 2 technologies on top of that. I mean, it's all, it seems to be all kind of a trade-off, but the point is, it's kinda brilliant to say, to just think about information, think about what kind of database, what kind of infrastructure enables the exchange of- - Yeah, let's say like you're operating an economy, and you need to have some thing that allows for the efficient, to have efficient value ratiosbetween products and services. So you've got this massive number of products and services, and need to, you can't just barter. 'Cause that would be extremely unwieldy. So you need something that gives you a ratio of exchange between goods and services. And then, something that allows you to shift obligations across time, like debt, debt and equity shift obligations across time.Then what does the best job of that? Part of the reason why I think there's some merit to Dogecoin, even though, it was obviously created as a joke, is that it actually does have a much higher transaction volume capability than Bitcoin. The costs of doing a transaction, the Dogecoin fee is very low. Like right now, if you wanna do a Bitcoin transaction, the price of doing that transaction is very high, so you could not use it effectively for most things.And nor could it even scale to a high volume. And when Bitcoin was started, I guess around 2008 or something like that, the internet connections were much worse than they are today, like order of magnitude. I mean, they were way, way worse in 2008. So like having a small block size or whatever it is, and a long synchronization time made sense in 2008, but, 2021, or fast forward 10 years, it's like, comically low.And I think there's some value to having a linear increase in the amount of currency that is generated. So, because some amount of the currency, if a currency is too deflationary or like, or should say if, if a currency is expected to increase in value over time, there's reluctance to spend it. 'Cause you're like, "Oh, if I, I'll just hold it and not spend it because its scarcity is increasing with time, so if I spend it now, then I will regret spending it.So I will just, you know, hoard all it." But if there's some dilution of the currency occurring over time, that's more of an incentive to use that as a currency. So Dogecoin just somewhat randomly has just a fixed a number of sort of coins or hash strings that are generated every year. So there's some inflation, but it's not a percentage at base.It's a fixed number, so the percentage of inflation will necessarily decline over time. I'm not saying that it's like the ideal system for a currency, but I think it actually is just fundamentally better than anything else I've seen, just by accident. - I like how you said around 2008, so you're not, some people suggest that you might be Satoshi Nakamoto.You've probably said you're not. Let me ask- - I'm not. - You're not, for sure. Would you tell us if you were? - Yes. - Okay. Do you think it's a feature or a bug that he's anonymous, or she, or they? It's an interesting kind of quirk of human history that there is a particular technology that is a completely anonymous inventor.Or creator. - Well, I mean, you can look at the evolution of ideas before the launch of Bitcoin and see who wrote about those ideas. And then, I don't know, obviously I don't know who created Bitcoin for practical purposes, but the evolution of ideas is pretty clear for that. And, it seems as though Nick Szabo is probably more than anyone else responsible for the evolution of those ideas.So, here he claims not to be Nakamoto, but I'm not sure, that's neither here nor there, but he seems to be the one more responsible for the ideas behind Bitcoin than anyone else. - So it's not, perhaps, like singular figures aren't even as important as the figures involved in the evolution of ideas that led to things.- Yeah. - Yeah. Perhaps it's sad to think about history, but maybe most names would be forgotten anyway. - What is a name anyway, it's a name, a name attached to an idea. What does it even mean really? - I think Shakespeare had a thing about roses and stuff, whatever he said. - "Rose by any other name would smell as sweet." (Lex laughing) - I got Elon to quote Shakespeare. I feel like I accomplished something today. - "Shall I compare thee to a summer's day?" (both laughing) - [Lex] I'm gonna clip that out instead. - Thou art more temporate and more fair. (both laughing) (Shivon speaking faintly) - Autopilot. Tesla autopilot- (Elon laughing) Tesla autopilot has been through an incredible journey over the past six years, or perhaps even longer in the minds of, in your mind, and the minds of many involved.- I think that's where we first like connected, really, was the autopilot stuff, autonomy and... - The whole journey was incredible to me to watch. 'Cause I knew, well, part of it is I was at MIT and I knew the difficulty of computer vision. And I knew the whole, I had a lotta colleagues and friends, about the DARPA challenge, and knew how difficult it is.And so there was a natural skepticism when I first drove a Tesla with the initial system based on Mobileye. I thought there's no way. So at first when I got in I thought "There's no way this car could maintain, like stay in the lane and create a comfortable experience." So my intuition initially was that the lane-keeping problem is way too difficult to solve.- [Elon] Oh lane-keeping, yeah, that's relatively easy. - But solve in the way that we just, we talked about previous, this prototype, versus a thing that actually creates a pleasant experience over hundreds of thousands of miles or millions. Yeah, so, I was proven wrong- - We had to wrap a lot of code around the Mobileye thing, it doesn't just work by itself.- I mean, that's part of the story of how you approach things sometimes. Sometimes you do things from scratch. Sometimes at first you kind of see what's out there, and then you decide to from scratch. That was one of the boldest decisions I've seen is both on the hardware and the software to decide to eventually go from scratch.I thought, again, I was skeptical of whether that's going to be able to work out 'cause it's such a difficult problem. And so it was an incredible journey, what I see now with everything, the hardware, the compute, the sensors, the things I maybe care and love about most is the stuff that Andrej Karpathy's leading with, the dataset selection, the whole data engine process, the neural network architectures, the way that's in the real world, that network is tested, validated, all the different test sets,versus the image net model of computer vision, like what's in academia is like real world artificial intelligence. - Andrej's awesome and obviously plays an important role, but we have a lot of really talented people driving things. Ashok is actually the head of autopilot engineering. Andrej's the director of AI.- Ai stuff, yeah. So yeah, I'm aware that there's an incredible team of just a lot going on. - People will give me too much credit, and they'll give Andrej too much credit. - And people should realize how much is going on under the- - Yeah, just a lot of really talented people. The Tesla Autopilot AI team is extremely talented.It's like some of the smartest people in the world. So, yeah, and we're getting it done. - What are some insights you've gained over those five, six years of autopilot about the problem of autonomous driving. So, you leaped in having some sort of first principles kinds of intuitions, but nobody knows how difficult the pro- - Yeah, I thought the self-driving problem would be hard, but it was harder than I thought.It's not like I thought it'd be easy, I thought it would be very hard, but it was actually way harder than even that. So, I mean want it comes down to at the end of the day is to solve self-driving you have to solve. You basically need to recreate what humans do to drive, which is humans drive with optical senses, eyes, and biological neural nets.And so in order to, that's how the entire road system is designed to work, with basically passive optical and neural nets, biologically. So, for actually, for full self driving to work, we have to recreate that in digital form. So we have to, that means cameras with advanced neural nets in silicon form. And then it will obviously solve for small cell driving.That's the only way, I don't think there's any other way. - But the question is what aspects of human nature do you have to encode into the machine, right? So you have to solve the perception problem, like detect, and then you first realize, what is the perception problem for driving? Like all the kinds of things you have to be able to see.Like what do we even look at when we drive? There's, I just recently heard, Andrej talked about, at MIT, about like car doors. I think it was the world's greatest talk of all time about car doors. The fine details of car doors, like what is even an open car door, man. So like the ontology of that, that's a perception problem.We humans solve that perception problem, and Tesla has to solve that problem. And then there's the control and the planning, coupled with the perception. You have to figure out like what's involved in driving, especially in all the different edge cases. Maybe you can comment on this, how much game theoretic kind of stuff needs to be involved, at a four-way stop sign? As humans, when we drive, our actions affect the world.- True. - It changes how others behave, most autonomous driving, you're usually just responding to the scene, as opposed to like really asserting yourself in the scene. Do you think... - I think these sort of control logic conundrums are not the hard part. Let's see... - [Lex] What do you think is the hard part in this whole beautiful complex problem? - It's a lot of freaking software man, and a lot of smart lines of code.For sure, in order to create an accurate vector space. You're coming from image space, which is like this flow of photons going to the camera, cameras and then since you have this massive bitstream in image space, and then you have to effectively compress the, a massive bitstream corresponding to photons that knocked off an electron in a camera sensor and turn that bitstream into a vector space.By vector space I mean, you've got cars and humans and lane lines and curves and traffic lights and that kind of thing. Once you have an accurate vector space, the control problem is similar to that of a video game, like a "Grand Theft Auto" or "Cyberpunk." If you have accurate vector space.It's, the control problem is, I wouldn't say it's trivial, it's not trivial, but it's it's not like some insurmountable thing. Having an accurate vector space is very difficult. - Yeah, I think we humans don't give enough respect to how incredible the human perception system is to mapping the raw photons to the vector space representation in our heads.- Your brain is doing an incredible amount of processing and giving you an image that is a very cleaned up image. Like when we look around here, you see color in the corners of your eyes, but actually your eyes have very few cones, cone receptors in the peripheral vision. Your eyes are painting color in the peripheral vision.You don't realize it, but they're, eyes are actually painting color and your eyes will also have, there's blood vessels and all sorts of gnarly things, and there's a blind spot, but do you see your blind spot? No, your brain is painting in the missing, the blind spot. You're gonna do these things online where you look here and look at this point and then look at this point, and it's, if it's in your blind spot, your brain will just fill in the missing bits. - So cool.The peripheral vision's so cool. - Yeah. - It makes you realize all the illusions, provision science, it makes you realize just how incredible the brain is. - The brain's doing a crazy amount of post-processing on the vision signals from your eyes. It's insane. And then even once you get all those vision signals, your brain is constantly trying to forget as much as possible.So human memory is perhaps the weakest thing about the brain is memory. So because memory is so expensive to our brain, and so limited, your brain is trying to forget as much as possible and distill the things that you see into the smallest amounts of information possible. So your brain is trying to not just get to a vector space, but get to a vector space that is the smallest possible vector space of only relevant objects.You can sort of look inside your brain, or at least I can like when you drive down the road, and try to think about what your brain is actually doing, - Yeah - consciously. It's like, you'll see a car, because you don't have cameras. You don't have eyes in the back of your head or the side, so you say like, you're basically, your head is like a, you basically have like two cameras on a slow gimbal.(both laughing) And eyesight's not that great. Okay? Human eyes are... And people are constantly distracted and thinking about things and texting and doing all sorts of things they shouldn't do in a car, changing the radio station. So, having arguments. When's the last time you looked right and left, and rearward, or even diagonally forward to actually refresh your vector space? So you're glancing around and what your mind is doing is trying to distill the relevant vectors, basically objects with a position and motion,and then editing that down to the least amount that's necessary for you to drive. - It does seem to be able to edit it down or compress even further into things like concept, so it's not, it's like it goes beyond, the human mind seems to go sometimes beyond vector space to sort of space of concepts, to where you'll see a thing, it's no longer represented spatially somehow, it's almost like a concept that you should be aware of.If this is a school zone, you'll remember that as a concept. Which is a weird thing to represent, but perhaps for driving you don't need to fully represent those things. Or maybe you get those kind of - Well you- - indirectly. - You need to established vector space and then actually have predictions for those vector spaces.Like you drive past say a bus and you see that there's people, before you drove past the bus you saw people crossing, or just imagine there's like a large truck or something blocking site. But before you came up to the truck you saw that there were some kids about to cross the road in front of the truck.Now you can no longer see the kids, but you would now know, okay, those kids are probably gonna pass by the truck and cross the road. Even though you cannot see them. So you have to have memory. You need to remember that there were kids there and you need to have some forward prediction of what their position will be.- It's a really hard problem - at the time of relevance. - So with occlusions and computer vision, when you can't see an object anymore, even when it just walks behind a tree and reappears, that's a really, really, I mean, at least in academic literature, it's tracking through occlusions, it's very difficult.- Yeah, we're doin' it. - [Lex] I understand this. So some of it- - It's like object permanence. The same thing happens with the humans with neural nets. When like a toddler grows up, there's a point in time where they develop, they have a sense of object permanence. So before a certain age, if you have a ball, or a toy or whatever, and you put it behind your back and you pop it out, before they have object permanence, it's like a new thing every time.It's like, "Whoa, this toy went poof, disappeared, and now it's back again." and they can't believe it. And that they can play peek-a-boo all day long because peek-a-boo's fresh every time. But then we figure out object permanence, then they realize, "Oh, no, the object is not gone.It's just behind your back." - Sometimes I wish we never did figure out object permanence. - Object permanence. Yeah, so that's a... - [Lex] That's an important problem to solve. - Yes. So, an important evolution of the neural nets in the car is memory across both time and space. Now you can't remember, you have to say how long do you want to remember things for.There's a cost to remembering things for a long time. So you could run out of memory to try to remember too much for too long. And then you also have things that are stale if you remember 'em for too long. And then you also need things that are remembered over time. So even if you, say have, for evidence sake, five seconds of memory on a time basis, but, let's say you you're parked at a light and you saw, use a pedestrian example, that people were waiting to cross the cross the road, and you can't quite see them because of an occlusion,but they might wait for a minute before the light changes for them to cross the road. You still need to remember that that's where they were, and that they're probably going to cross road type of thing. So even if that exceeds your time-based memory, it should not exceed your space of memory. - And I just think the data engine side of that, so getting the data to learn all of the concepts that you're saying now, is an incredible process.It's this iterative process of just, there's this HydraNet of many- - HydraNet. We're changing the name to something else. - Okay. Alright. I'm sure it will be equally as "Rick and Morty," like. - Yeah. We've re-architected the neural nets in the cars so many times, it's crazy.- Oh, so every time there's a new major version, you'll rename it to something more ridiculous or, or memorable and beautiful, sorry. Not ridiculous of course. - If you see the full like array of neural nets that are operating the cars, it kinda boggles the mind. There's so many layers. It's crazy.We started off with simple neural nets that were basically image recognition on a single frame from a single camera, and then trying to knit those together with, with C. I should say, we were really familiar running C here, 'cause C++ is too much overhead, and we have our own C compiler. So, to get maximum performance we actually wrote our own C compiler and are continuing to optimize our C compiler for maximum efficiency.In fact, we've just recently done a new rev on the C compiler that will compile directly to our autopilot hardware. - So you wanna compile the whole thing down with your own compiler? - Yeah. - So efficiency here, 'cause there's all kinds of computers, CPU, GPU, there's like basic types of things and you have to somehow figure out the scheduling across all of those things.And so you're compiling the code down - Yeah. - that does all, okay. So that's why there's a lotta people involved. - There's a lot of hardcore software engineering at a very sort of bare metal level. 'Cause we're trying to do a lot of compute that's constrained to the our full self-driving computer.And we wanna try to have the highest frames per second possible in a sort of very finite amount of compute and power. We really put a lot of effort into the efficiency of our compute. So there's actually a lot of work done by some very talented software engineers at Tesla that, at a very foundational level to improve the efficiency of compute and how we use the trip accelerators, which are basically doing matrix math, dot products, like a bazillion dot products.And it's like, one of our neural nets is like, compute wise, like 99% dot products. - And you wanna achieve as many high frame rates, like a video game, you want - Yeah. - full resolution, higher frame. - High frame rate, low latency, low jitter. I think one of the things we're moving towards now is no post-processing of the image through the image signal processor.What happens for cameras is that, well almost all cameras, is they there's a lot of post-processing done in order to make pictures look pretty. And so we don't care about pictures looking pretty. We just want the data. So we're moving just raw photon counts. The image that the computer sees is actually much more than what you'd see if you represent it on a camera, it's got much more data.And even in very low light conditions, you can see that there's a small photon count difference between this spot here and that spot there, which means that, so it can see in the dark incredibly well, because it can detect these tiny differences in photon counts. Like much better than you could possibly imagine.We also save 13 milliseconds on latency. - [Lex] From removing the post-processing on the image? - Yes. - Yeah. - 'Cause we've got eight cameras and then there's roughly, I don't know, one and a half milliseconds or so, maybe 1.6 milliseconds of latency for each camera. Basically bypassing the image processor gets us back 13 milliseconds of latency, which is important.And we track latency all the way from photon hits the camera, to all the steps that it's gotta go through to get, go through the various neural nets and the C code, and there's a little bit of C++ there as well. Well, I can, maybe a lot, but it, the core stuff is, the heavy-duty compute is all in C.And so we track that latency all the way to an outward command to the drive unit to accelerate the brakes, to slow down the steering, turn left or right. 'Cause you gotta output a command, that's gotta go to a controller, and like some of these controllers have an update frequency that's maybe 10 Hertz or something like that, which is slow.That's like now you lose a hundred milliseconds potentially. So then we wanna update the drivers on the steering and braking control to have more like 100 Hertz instead of 10 Hertz, then you've got a 10 millisecond latency instead of 100 milliseconds worst-case latency. And actually, jitter is more of a challenge than latency, 'cause latency is, you can anticipate and predict, but if you've got a stackup of things going from the camera to the computer, through then a series of other computers,and finally to an actuator on the car; if you have a stackup of tolerances, of timing tolerances, then you can have quite a variable latency, which is called jitter. And that makes it hard to anticipate exactly how you should turn the car or accelerate because, if you've got maybe 150, 200 milliseconds of jitter, then you could be off by 2.2 seconds.And this could make a big difference. - So you have to interpolate somehow to deal with the effects of jitter, so they can make robust control decisions. So the jitters and the sensor information, or the jitter can occur at any stage in the pipeline. - If you have just, if you have fixed latency, you can anticipate and like say, "Okay, we know what that our information is," for argument's sake, "150 milliseconds stale." For argument's sake, 150 milliseconds from photons taking camera to where you can measure a change in the acceleration of the vehicle. Then you can just say, "Okay, well we're gonna, we know it's 150 milliseconds, so we're gonna take that into account and compensate for that latency." However, if you've got then 150 milliseconds of latency, plus 100 milliseconds of jitter, which could be anywhere from zero to 100 milliseconds on top. So then your latency could be from 150, 250 milliseconds, now you've got 100 milliseconds that you don't know what to do with. That's basically random.So, getting rid of jitter is extremely important. - And that affects your control decisions and all of those kinds of things. Okay. - Yeah, the cars just gonna fundamentally maneuver better with lower jitter. - [Lex] Got it. - The cars will maneuver with super human ability and reaction time, much faster than a human.I mean, I think over time, the autopilot, full self-driving will be capable of maneuvers that are far more than what like James Bond could do in like the best movie, type of thing. - That's exactly what I was imagining in my mind, as you said it. - It's like impossible maneuvers that a human couldn't do.- Well, let me ask sort of a, looking back the six years, looking out into the future, based on your current understanding, how hard do you think this full self-driving problem, when do you think Tesla will solve level four FSD? - I mean, it's looking quite likely that it'll be next year. - And what does the solution look like? Is it the current pool of FSD beta candidates? They start getting greater and greater as they have been, degrees of autonomy.And then there's a certain level beyond which they can do their own, they can read a book. - Yeah. I mean, you can see, anybody who's been following the full self-driving beta closely will see that the rate of disengagements has been dropping rapidly. So, like there's engagement B where the driver intervenes to prevent the car from doing something - [Lex] Right.dangerous potentially. So the interventions per million miles has been dropping dramatically. And that trend looks like it happens next year is that the probability of an accident on FSD is less than that of the average human, and then significantly less than that of the average human. So, it certainly appears like we will get there next year.Then there's gonna be a case of, okay, well, we not have to prove this to regulators and prove it to, and we want a standard that is not just equivalent to a human, but much better than the average human. I think it's gotta be at least two or three times higher safety than a human. Two or three times lower probability of injury than a human before we would actually say like, "Okay, it's okay to go." It's not gonna be equivalent, it's gonna be much better. - So if you look, FSD 10.6 just came out recently, 10.7's on the way, maybe 11 is on the way somewhere in the future. - Yeah. We were hoping to get 11 out this year, but it's, 11 actually has a whole bunch of fundamental rewrites on the neural net architecture and some fundamental improvements in creating vector space.- So there is some fundamental leap that really deserves the 11. I mean, that's a pretty cool number. - Yeah. 11 would be a single stack for all, one stack to rule them all. - A single stack. But there are just some really fundamental neural net architecture changes that will allow for much more capability.At first they're gonna have issues. Like we have this working on like sort of alpha software and it's good, but it's, it's basically taking a whole bunch of C, C++ code and leading a massive amount of C++ code and replacing it with the neural net. And Andrej makes this point a lot, which is like neural nets are kind of eating software.Over time there's less and less conventional software, more and more neural net. Which is still software, but it's, still comes out to lines of software. But, just more neural net stuff, and less, heuristics basically. More matrix based stuff, and less heuristics based stuff. One of the big changes will be, right now the neural nets will deliver a giant bag of points to the C++, or C and C++ code.- [Lex] Yeah. - We call it the giant bag of points. - [Lex] Yeah. And it's like, so you got a pixel and something associated with that pixel, like this pixel is probably car, this pixel is probably landline. Then you've got to assemble this giant bag of points in the C code and turn it into vectors. And it does a pretty good job of it, but it's, we wanna just, we need another layer of neural nets on top of that to take the giant bag of points and distill that down to a vector space in the neural net part of the software,as opposed to the heuristics part of the software. This is a big improvement. - [Lex] Neural net's all the way down, so you want. - It's not even all neural nets, but it's, this is a game changer to not have the bag of points, the giant bag of points, that has to be assembled with many lines of C, C++, and have a neural net just assemble those into a vector.So the neural net is outputting much, much less data, it's outputting, this is a lane line, this is a curb, this is drivable space, this is a car, this is a pedestrian or cyclist or something like that. It's outputting, it's really outputting proper vectors to the C, C++ control code, as opposed to, sort of, constructing the vectors in C.Which we've done, I think, quite a good job of, but it grew kinda hitting a local maximum on the, how well the C can do this. So this is really a big deal. And just all of the networks in the car need to move to Surround Video, there's still some Legacy Networks that are not Surround Video. And all of the training needs to move to Surround Video, and the efficiency of the training, it needs to get better, and it is.And then we need to move everything to raw photon counts, as opposed to processed images. - [Lex] Okay. So if you- - Which is quite a big reset on the training, 'cause the system's trained on post-process imaged images. So we need to redo all the training to train against the raw photon counts, instead of the post-processed image.- So ultimately, it's kind of reducing the complexity of the whole thing. So, reducing. - Yep. Lines of code will actually go lower. - Yeah, that's fascinating. So you do infusion of all the sensors, so reducing the complexity of having to deal with these- - [Elon] Infusion of the cameras. - Sorry. - It's all cameras really.- Right, yes. Same with humans. - Yeah. - Well, I guess we got ears too, okay. - Yeah, we'll actually need to incorporate sound as well. 'Cause you know, you need to listen for ambulance sirens or firetrucks. If somebody, yelling at you or something, I don't know. It just, there's a little bit of audio that needs to be incorporated as well.- Do you need to go to bathroom break? - [Elon] Yeah, sure, let's take a break. - Okay. - [Elon] Honestly, frankly, the ideas are the easy thing, and the implementation is the hard thing. The idea of going to the moon is the easy part, but going to the moon is the hard part. - [Lex] Is the hard part. - And there's a lot of like hardcore engineering that's gotta get done at the hardware and software level.Like I said, optimizing the C compiler and just, cutting out latency everywhere. If we don't do this, the system will not work properly. So, the work of the engineers doing this, they are like the unsung heroes. But they are critical to the success of the situation. - I think you made it clear. I mean, at least to me, it's super exciting, everything that's going on outside of what Andrej is doing.Just the whole infrastructure of the software. I mean, everything is going on with data engine, whatever it's called, the whole process is just a work of art. - The sheer scale of it is, it boggles the mind. The training, the amount of work done with, we've written all this custom software for training and labeling, and to do order labeling.Order labeling is essential. 'Cause, especially when you've got like Surround Video, it's very difficult to label Surround Video from scratch is extremely difficult. Take humans such a long time to even label one video clip, like several hours. Or the order labeler, it basically will just apply heavy duty, a lot of compute to the video clips, to pre-assign and guess what all the things are that are going on in the Surround Video.- [Lex] And there's like correcting it. - Yeah, and then all the human has to do is like tweak, like say, adjust what is incorrect. This is like, increases productivity by 100 or more. - Yeah. So you've presented Tesla Bot as primarily useful in the factory. First of all, I think humanoid robots are incredible from a fan of robotics.I think the elegance of movement that humanoid robots, that bipedal robots show are just so cool. It's really interesting that you're working on this and also talking about applying the same kind of, all the ideas, of some of which you've talked about, with data engine, all the things that we're talking about, with Tesla autopilot, just transferring that over to the, just yet another robotics problem.I have to ask since I care about human robot interactions, so the human side of that. So you've talked about mostly in the factory. Do you see as part of this problem that Tesla Bot has to solve is interacting with humans and potentially having a place like in the home. So, interacting, not just, - Sure.- not replacing labor, but also like, I don't know, being a friend or an assistant. - [Elon] I think the possibilities are endless. Yeah, I mean, it's obviously, it's not quite in Tesla's primary mission direction of accelerating sustainable energy, but it is an extremely useful thing that we can do for the world, which is to make a useful humanoid robot that is capable of interacting with the world and helping in many different ways.So in factories, and really just, I mean, I think, if you say, extrapolate to many years in the future, I think work will become optional. There's a lot of jobs that, if people weren't paid to do it, they wouldn't do it. Like it's not, it's not fun, necessarily. If you're washing dishes all day, it's like, eh.Even if you really like washing dishes, do you really wanna do it for eight hours a day every day? Probably not. And then there's like dangerous work, and basically if it's dangerous, boring, has like potential for repetitive stress injury, that kind of thing, then that's really where humanoid robots would add the most value initially.So that's what we're aiming for is to, for the humanoid robots to do jobs that people don't voluntarily want to do. And then we'll have to pair that, obviously, with some kind of universal, basic income in the future. So, I think. - Do you see a world when there's like hundreds of millions of Tesla Bots doing different, performing different tasks throughout the world? - Yeah, I haven't really thought about it that far into the future, but I guess that there may be something like that.- Can I ask a wild question? So, the number of Tesla cars has been accelerated and has been close to 2 million produced. Many of them have autopilot. - [Elon] I think we're over 2 million now. - Yeah. Do you think there'll ever be a time when there'll be more Tesla Bots than Tesla cars? - Yeah. Actually, it's funny you ask this question 'cause normally I do try to think pretty far into the future, but I haven't really thought that far into the future with the Tesla Bot, or it's codenamed Optimus,I call it Optimus Subprime, because it's not like a giant transformer robot. But it's meant to be a general purpose help robot. And basically, the things that were, basically, Tesla, I think, has the most advanced real-world AI for interacting with the real world, which we've developed as a function to make self-driving work.And so, along with custom hardware and, like a lotta hardcore low-level software to have it run efficiently and be power efficient 'cause, it's one thing to do neural nets if you've got a gigantic server room with 10,000 computers, but now, let's say you just, you have to now distill that down into one computer that's running at low power in a humanoid robot or a car.That's actually very difficult and a lotta hardcore soft work is required for that. So since we're kind of like solving the navigate the real world with neural nets problem for cars, which are kinda like robots with four wheels, then it's like kind of a natural extension of that is to put it in a robot with arms and legs.And actuators. The two hard things are, you basically need to make the, have the robot be intelligent enough to interact in a sensible way with the environment. So you need real real world AI, and you need to be very good at manufacturing, which is a very hard problem. Tesla's very good at manufacturing, and also has the real world AI, so making the humanoid robot work is, basically it means developing custom motors and sensors that are different from what a car would use.I think we have the best expertise in developing advanced electric motors and power electronics. So, it just has to be for humanoid robot application, not a car. - Still, you do talk about love sometimes. So let me ask, this isn't like for like sex robots or something- - [Elon] Love is the answer. - Yes. There is something compelling to us, not compelling, but we connect with humanoid robots, or even legged robot, like with a dog, in shapes of dogs.It just, it seems like there's a huge amount of loneliness in this world. All of us seek companionship with other humans, friendship and all those kinds of things. We have a lot of here in Austin, a lot of people have dogs. - [Elon] That's right. - There seems to be a huge opportunity to also have robots that decrease the amount of loneliness in the world, or help us humans connects with each other.So, in a way that dogs can. Do you think about that with Tesla Bot at all, or is it really focused on the problem of performing specific tasks? Not connecting with humans? - I mean, to be honest, I have not actually thought about it from the companionship standpoint, but I think it actually would end up being, it could be actually a very good companion.And it could develop a personality over time that is unique. It's not just all the robots are the same. And that personality could evolve to be, match the owner or the, I guess the owner. Whatever you wanna call it. The companion, the human. - The other half, right? In the same way that friends do. See, I think that's a huge opportunity.I think- - Yeah, no, that's interesting. 'Cause there's a Japanese phrase; wabi-sabi, the subtle imperfections are what makes something special. And the subtle imperfections of the personality of the robot, mapped to the subtle imperfections of the robot's human friend, dunno, owner sounds like maybe the wrong word, but, could actually make an incredible buddy basically.- [Lex] And in that way, the imperfections- - Like R2-D2 or a C-3PO sort of thing. - So from a machine learning perspective, I think the flaws being a feature is really nice. You could be quite terrible at being a robot for quite a while in the general home environment or all in the general world. And that's kind of adorable and that's, those are your flaws, and you fall in love with those flaws.It's very different than autonomous driving where it's a very high stakes environment, you cannot mess up. And so it's, yeah, it's more fun to be a robot in the home. - Yeah, in fact, if you think of like a C-3PO and R2-D2, they actually had a lot of like flaws and imperfections and silly things and they would argue with each other.- Were they actually good at doing anything? I'm not exactly sure. - They definitely added a lot to the story. But there sort of quirky elements and, that they would make mistakes and do things, it would just, it made them relatable, I don't know. Endearing. So yeah, I think that that could be something that, it probably would happen.But our initial focus is just to make it useful. I'm confident we'll get it done, I'm not sure what the exact timeframe is, but we'll probably have, I don't know, a decent prototype towards the end of next year or something like that. - And it's cool that it's connected to Tesla, the car.- Yeah, it's using a lotta, it would use the autopilot inference computer and a lot of the training that we've done for the four cars, in terms of recognizing real world things, could be applied directly to the robot. But there's a lot of custom actuators and sensors that need to be developed. - And an extra module on top of the vector space for love.- Ah, yeah. - That's missing. Okay. - We could add that to the car too. - That's true. Yeah, it could be useful in all environments. Like you said, a lot of people argue in the car, so maybe we can help 'em out. You're a student of history, fan of "Dan Carlin's Hardcore History" podcast.- [Elon] Yeah. That's great. - Greatest podcast ever. - Yeah, I think it is, actually. - It almost doesn't really count as a podcast. - [Elon] It's more like a audio book. - Yeah. So you were on the podcast with Dan, I just had a chat with him about it. He said you guys went military and all that kind of stuff.- Yeah, it was basically, it should be titled engineer wars. Essentially, when there's a rapid change in the rate of technology, then engineering plays a pivotal role in victory in battle. - How far back in history did you go? Did you go to World War II? - Well, it was supposed to be a deep dive on fighters and bomber technology in World War II, but that ended up being more wide-ranging than that.'Cause I just went down the, a total rat hole of like studying all of the fighters and bombers in World War II, and the constant rock, paper, scissors game that one country would make this plane, and they'd make a plane to beat that, and they'd try to make a plane to beat that, and then they'll...And really what matters is like the pace of innovation, and also access to high quality fuel and raw materials. So, like Germany had like some amazing designs, but they couldn't make them because they couldn't get the raw materials. And they had a real problem with the oil and fuel, basically, the fuel quality was extremely variable.- So the design wasn't the bottleneck, it was- - Yeah, the U.S. had kick-ass fuel, that was very consistent, the problem is, if you make a very high performance aircraft engine, in order to make it high performance, you have to the fuel, the aviation gas, has to be a consistent mixture. And it has to have a high octane.High octane is the most important thing, but also can't have like impurities and stuff 'cause you'll foul up the engine. And the German just never had good access to oil. They try to get it by invading the caucuses, but that didn't work too well. - That never works well. - Didn't work out for them.(woman speaking faintly) Nice to meet you. Germany was always struggling with basically shitty oil, and so then they could not, they couldn't count on high quality fuel for their aircraft. So then they had to have all these additives and stuff. Whereas the U.S. had awesome fuel, and they provided that to Britain as well.So, that allowed the British and the Americans to design aircraft engines that were super high-performance, better than anything else in the world. Germany could design the engines, they just didn't have the fuel. And then also the likes of the, the quality of the aluminum alloys that they were getting was also not that great, and so, yeah.- [Lex] You talked about all this with Dan? - Yep. - Awesome. Broadly looking at history, when you look at Genghis Khan, when you look at Stalin, Hitler, the darkest moments of human history, what do you take away from those moments? Does it help you gain insight about human nature, about human behavior today? Whether it's the wars or the individuals, or just the behavior of people, any aspects of history.- Yeah. I find history fascinating. There's just a lot of incredible things that have been done, good and bad, that they just help you understand the nature of civilization, and individuals, and... - Does it make you sad that humans do these kinds of things to each other? You look at the 20th century, World War II, the cruelty of the abuse of power.Talk about communism, Marxism, and Stalin. - I mean, some of these things do, I mean, if you, there's a lot of human history, but most of it is actually people just getting on with their lives, and it's not like human history is just non-stop war and disaster, those are actually just, those are intermittent and rare, and if they weren't then humans would soon cease to exist.But there's just that, wars tend to be written about a lot. Whereas something being like, well, a normal year where nothing major happened doesn't get written about much, but that's, most people just like farming and kinda living their life. Being a villager somewhere. And every now and again, there's a war.I would have to say, there aren't very many books that I, where I just had to stop reading, 'cause it was just too dark. But the book about "Stalin The Court Of The Red Star," I had stopped reading, it was just too dark. Rough. - Yeah. The 30s. There's a lot of lessons there to me, in particular that it feels like humans, all of us have that zeal, Solzhenitsyn line, that the line between good and evil runs to the heart in every man that all of us are capable of evil, all of us are capable of good,it's almost like this kind of responsibility that all of us have to tend towards the good. And so, to me, looking at history is almost like an example of, look, you have some charismatic leader that convinces you of things, is too easy, based on that story to do evil, onto each other, onto your family onto others.And so it's like our responsibility to do good. It's not like now somehow different from history, that can happen again, all of it can happen again. And yes, most of the time you're right. I mean, the optimistic view here is mostly people are just living life. And as you've often memed about, the quality of life was way worse back in the day, and it keeps improving over time, through innovation, through technology, but still it's somehow notable that these blimps of atrocities happen.- Sure. Yeah, I mean, life was really tough for most of history. I mean, probably for most of human history, a good year would be one where not that many people in your village died of the plague, starvation, freezing to death, or being killed by a neighboring village. It's like, "Well, it wasn't that bad." It was only like, "You know, we lost 5% this year. It was a good year." - Yeah. - That would be par for the course. Just not starving to death would have been the primary goal of most people throughout history. Just making sure we'll have enough food to last through the winter and not get, freeze or whatever.Now food is plentiful. We have an obesity problem. - Well, yeah, the lesson there is to be grateful for the way things are now for some of us. We've spoken about this offline. I'd love to get your thought about it here. If I sat down for a long form in person conversation with the President of Russia, Vladimir Putin, would you potentially want to call in for a few minutes to join in on a conversation with him, moderated and translated by me? - Sure. Yeah.Sure, I'd be happy to do that. - You've shown interest in the Russian language. Is this grounded in your interest in history of linguistics culture, general curiosity? - [Elon] I think it sounds cool. - Sounds cool, not looks cool. It takes a moment to read Cyrillic. Once you know what the Cyrillic characters stand for, actually, then reading Russian becomes a lot easier 'cause there are a lot of words that are actually the same.Like bank is bank. - So find the words that are exactly the same and now you start to understand Cyrillic, yeah. - If you can sound it out, then it's much, there's at least some commonality of words. - What about the culture? You love great engineering, physics. There's a tradition of the sciences there.When you look at the 20th century, from rocketry. So, some of the greatest rockets, some of the space exploration has been done in the Soviet, in the former Soviet Union. - Yeah. - So, do you draw inspiration from that history? Just how this culture, that in many ways, I mean, one of the sad things is, because of the language, a lot of it is lost to history, because it's not translated, all those kinds of, because it is in some ways an isolated culture, it flourishes within it's borders.- [Elon] Yeah. - So do you draw inspiration from those folks, from the history of science engineering there? - Yeah. I mean, the Soviet Union, Russia, and Ukraine as well, have a really strong history in space flight, like some of the most advanced and impressive things in history were done by the Soviet Union.One cannot help but admire the impressive rocket technology that was developed. After the sort of fall of the Soviet Union, there's much less that happened, still things are happening, but it's not quite at the frenetic pace that it was happening before the Soviet Union kind of dissolved into separate republics.- Yeah. I mean, there's the Roscosmos, the Russian, the agency. I look forward to a time when those countries, with China, are working together, the United States, they're all working together, maybe a little bit of friendly competition, but. - I feel like friendly competition is good. Governments are slow and the only thing slower than one government is a collection of governments.(Lex laughing) - Yeah. - The Olympics would be boring if everyone just crossed the finishing line at the same time. - Yeah. - Nobody would watch. - [Lex] Yeah. - And people wouldn't try hard to run fast and stuff. So, I think friendly competition is a good thing. - This is also a good place to give a shout out to a video titled "The Entire Soviet Rocket Engine Family Tree" by Tim Dodd, AKA Everyday Astronaut.It's like an hour and a half. It gives a full history of Soviet rockets. And people should definitely go check out and support Tim in general, that guy's super excited about the future, super excited about space flight, every time I see anything by him I just have a stupid smile on my face, 'cause he's so excited about stuff.- Yeah, Tim Dodd is - I love people like that. - really great if you're interested in anything to do with space. He's, in terms of explaining rocket technology to your average person, he's awesome. The Best, I'd say. I should say, the whole reason I switched us from, Raptor at one point was gonna be a hydrogen engine, but hydrogen has a lot of challenges.It's very low density. It's a deep cryogen, so it's only liquid very close to absolute zero. Requires a lot of insulation. So it was a lot of challenges there. And I was actually reading a bit about Russian rocket engine development. At least the impression I had was that Soviet Union, Russia, and Ukraine primarily were actually in the process of switching to Methalux.And there were some interesting test and data for ISP, they were able to get up to like a 382nd ISP with the Methalux engine. And I was like, "Whoa, okay, that's, that's actually really impressive." So I think we could, you could actually get a much lower cost, an optimizing cost per ton to orbit, cost per to Mars.I think methane option is the way to go. And I was partly inspired by the Russian work on the test ends, with Methalux engines. - And now for something completely different. Do you mind doing a bit of a meme review in the spirit of the great, the powerful Pewdiepie? Let's say one to 11, - Okay. - just go over a few documents printed out.- [Elon] We can try. - [Lex] Let's try this. I present to you document numero uno. (Elon laughing) - Okay. - [Lex] Vlad The Impaler discovers marshmallows. - Yeah, that's not bad. - You get it, because he likes impaling things. - Yes, I get it. Yes, I get it, I don't know, three, whatever. - [Lex] Oh, that's not very good.This is ground in some engineering, some history. (Elon laughing) - Yeah, I give this an 8 out of 10. - [Lex] What do you think about nuclear power? - I'm in favor of nuclear power. In a place that is not subject to extreme natural disasters. I think it's a, new nuclear power is a great way to generate electricity.I don't think we should be shutting down nuclear power stations. - [Lex] Yeah, but what about Chernobyl? - Exactly. I think people, there's like a lot of fear of radiation and stuff. I guess, the problem is a lot of people just don't, they didn't study engineering or physics, so they don't, just the word radiation just sounds scary, you know? So they don't, they can't calibrate what radiation means.But radiation is much less dangerous than you'd think. For example, Fukushima, when the Fukushima problem happened, due that tsunami. I got people in California asking me if they should worry about radiation from Fukushima. And I'm like, definitely not, not even slightly, not at all. That is crazy. And just to show this is how, the dangers is so much overplayed compared to what it really is that I actually flew to Fukushima.And, actually, I donated a solar power system for a water treatment plant. And I made a point of eating locally grown vegetables on T.V. in Fukushima. I'm still alive. Okay. - So it's not even that the risk of these events is low, but the impact of them is- - The impact is greatly exaggerated. - It' human nature.- People don't know what radiation is, I've had people ask me, "What about radiation from cell phones causing brain cancer?" I'm like, "When you say radiation, do you mean photons or particles?" They're like, dunno, "What do you mean photons particles?" "Do you mean, let's say photons. What frequency or wavelength?" And they're like, "No, I have no idea." "Do you know that everything's radiating all the time?" They're like, "What do you mean?" "Like, everything's radiating all the time." Photons are being emitted by all objects all the time, basically. And if you wanna know what it means to stand in front of nuclear fire, go outside.The sun is a gigantic thermonuclear reactor that you're staring right at it. Are you still alive? Yes. Okay. Amazing. - Yeah, I guess radiation is one of the words that could be used as a tool to fear monger by certain people. That's it. - I think people just don't understand. - I mean, that's the way to fight that fear, I suppose, just to understand, just to learn.- Yeah, just say, okay, how many people have actually died from nuclear accidents? It's like practically nothing, and, say how many people have died from coal plants? And it's a very big number. Obviously we should not be starting up coal plants and shutting down nuclear plants, just doesn't make any sense at all.Coal plants, I don't know, a hundred to a thousand times worse for health than nuclear power plants. - You wanna go to the next one? It's really bad. That 90, 180 and 360 degrees, everybody loves the math. Nobody gives a shit about 270. - It's not super funny. I don't know, like two or three.- [Lex] Yeah. This is not, LOL situation. (both laughing) - [Lex] Yeah. (Elon laughing) - That one's pretty good. - [Lex] The United States oscillating between establishing and destroying dictatorships. It's like a metro, is that metro- - Yeah, metronome. Yeah, it's, I dunno, a 7 out of 10. It's kinda true.- This is kinda personal for me. Next one. - Oh, man, is this Laika. - [Lex] Yeah, well, no, this is- - Or it's referring to Laika or something. - [Lex] It's Laika's husband. - Husband, yeah. - [Lex] Hello? Yes, this is dog. Your wife was launched into space. And then the last one is him with his eyes closed and a bottle of vodka.- Yeah, Laika didn't come back. - [Lex] No. They don't tell you the full story of, the impact it had on the loved ones. - True. - That one gets an 11 from me. It just keeps goin', on the Russian theme. First man in space, nobody cares. First man on the moon. - Well, I think people do care. - [Lex] I know, but.- Yuri Gagarin's name will be forever in history. I think. - There is something special about placing, stepping foot onto another totally foreign land. It's not the journey, like people that explore the oceans. It's not as important to explore the oceans as to land in a whole new continent. - [Elon] Yeah.- [Lex] Oh this is about you. (Elon laughing) Oh yeah. I'd love to get your comment on this. Elon Musk after sending $6.6 billion to the UN to end world hunger. "You have three hours." - Yeah, well, I mean obviously $6 billion is not gonna end world hunger. I mean, the reality is at this point the world is producing far more food than it can really consume.We don't have a caloric constraint to this point. So where there is hunger, it is almost always due to civil war, or strife, or some like, it's not a thing that is extremely rare for it to be just a matter of, lack of money. There's a civil war in some country, and one part of the country's literally trying to starve the other part of the country.- So it's much more complex than something that money could solve. It's geopolitics, it's a lot of things, it's human nature, it's governments, it's monies, monetary systems, all that kinda stuff. - Yeah. Food is extremely cheap these days. I mean, the U.S. at this point, among low income families, obesity is actually now the problem. It's not, obviously it's not hunger, it's too much, too many calories. It's not that nobody's hungry anywhere, it's just, this is not a simple matter of adding money and solving it. - [Lex] What do you think that one gets? Is getting? - Two. - [Lex] Just going after empires.World, "Where did you get those artifacts?" The British Museum. It's a shout out to "Monty Python." "We found them." - Yeah. The British Museum is, it's pretty great. I mean, admittedly Britain did take these historical artifacts from around the world and put them in London, but it's not like people can't go see them.So, it is a convenient place to see these ancient artifacts is London, for a large segment of the world. So I think, unbalanced, the British Museum is net good. Well, I'm sure that a lot of countries are arguing about that. - [Lex] Yeah. - It's like, you wanna make these historical artifacts accessible to as many people as possible.And the British Museum, I think does a good job of that. - Even if there's a darker aspect to like the history of empire in general, whatever the empire is, however things were done. It is the history that happened. You can't sort of erase that history, unfortunately. You can just become better in the future.Is the point. - Yeah, I mean, well how are we gonna pass moral judgment on these things? If one is gonna judge, say the Russia Empire, you gotta judge what everyone was doing at the time, and how were the British relative to everyone? And I think that the British would actually get a relatively good grade, relatively good grade, not in absolute terms, but compared to what everyone else was doin', they were not the worst.Like I said, you gotta look at these things in the context of the history at the time and say, "What were the alternatives, and what are you comparing it against?" - Yes. - And I do not think it would be the case that Britain would get a bad grade, when looking at history at the time. Now if you judge history from what is morally acceptable today, you're basically are gonna give everyone a failing grade.I'm not clear. I don't think anyone would get a passing grade in their morality of, you could go back 300 years ago, who is getting a passing grade? Basically no one. - [Lex] And we might not get a passing grade from generations - Yeah. Exactly. - [Lex] that come after us. What does that one get? - Sure. A six, a seven.- For the "Monty Python," maybe. - [Elon] I always 'Monty Python," they're great. The "Life of Brian" and the "Quest for the Holy Grail" are incredible. - Yeah. Yeah. - Damn, those are serious eyebrows. - [Lex] Brezhnev. How important, do you think, - Damn. - [Lex] is facial hair to great leadership? You got a new haircut.How does that affect your leadership? - [Elon] I don't know. Hopefully not. It doesn't. - [Shivon] Is that the second, no one? - Yeah, the second is no one. - [Elon] There is no one competing with Brezhnev. - No one two. - Those are like epic eyebrows. Sure. - [Lex] That's ridiculous. - Give it a six or seven, I dunno.- [Lex] I like this, Shakespeare analysis of memes. - Brezhnev, he had a flare for drama as well. German joke. - [Lex] Yeah, yeah. It must come from the eyebrows. Alright. Invention, great engineering. Look what I invented. That's the best thing since rip up bread. - Yeah. - 'Cause they invented sliced bread.Am I just explaining memes at this point? (all laughing) This is what my life has become. - [Shivon] He's a memelord, you're a meme explainer. - [Lex] I'm a meme, like a scribe, that runs around with the kings and just writes down memes. - I mean, when was the cheeseburger invented? That's an epic invention.- [Lex] Yeah. - Like, wow. - [Lex] Versus just like a burger? - Or a burger, I guess a burger in general. - Then there's, what is the burger? What's a sandwich? And then you start getting is a pizza a sandwich? And what is the original? It gets into an ontology argument. - Yeah, but everybody knows if you order a burger, or cheeseburger, or whatever, and you get tomato and some lettuce and onions and whatever, and mayo and ketchup and mustard, it's like epic.- Yeah, but I'm sure they've had bread and meat separately for a long time. And it was kind of a burger on the same plate, but somebody who actually combined them into the same thing and then bite it and hold it, makes it convenient. It's a materials problem. Like your hands don't get dirty and whatever.Yeah, it's brill- (Shivon talking faintly) That is not what I would've guessed. - But everyone knows, if you order a cheeseburger, you know what you're getting, it's not like some obtuse, well, I wonder what I'll get. Fries are, I mean, great. I mean, they're the devil, but fries are awesome.Yeah, pizza is incredible. - Food innovation doesn't get enough love. - Yeah. - I guess is what we're getting at. - [Elon] It's great. - What about the Matthew McConaughey, Austinite here? President Kennedy, "Do you know how to put men on the moon yet?" NASA, "No." President Kennedy, "Be a lot cooler if you did." - Pretty much, sure. Six, six or seven, I suppose. - [Lex] And this is the last one. - That's funny. - [Lex] Someone drew a bunch of dicks all over the walls. Sistine Chapel, Boys bathroom. - Sure, I'll give it a nine. It's really true. - This is our highest ranking meme for today. - [Elon] I mean, it's true, how did they get away with it? - Lotsa nakedness.- I mean, dick pics are, I mean, just something throughout history. As long as people can draw things, there's been a dick pic. - It's a staple of human history. - It's a staple. Consistent throughout human history. - You tweeted that you aspire to comedy, you're friends with Joe Rogan. Might you do a short standup comedy set at some point in the future? Maybe open for Joe? Something like that? Is that- - Really? Stand up? Actual just full-on stand up? - [Lex] Full-on stand up.Is that in there or is that? - I've never thought about that. - It's extremely difficult, at least that's what like Joe says, and the comedians say. - [Elon] Huh? I wonder if I could. - Only one way to find out. - I have done standup for friends, just impromptu, I'll get on like a roof, and they do laugh, but they're all friends too.So, I don't know if you got a room of strangers. Are they gonna actually also find it funny, but I could try. See what happens. - I think you'd learn something either way. - Yeah. - I kinda love both when and when you do great, just watching people, how they deal with it. It's so difficult. You're so fragile up there.It's just you. And you think you're gonna be funny and when it completely falls flat, it's just, it's beautiful to see people deal with that. - I think I might have enough material to do stand up. I've never thought about it, but I might have enough material. I don't know, like 15 minutes or something.- Oh yeah. Yeah. Do a Netflix special. (Elon laughing) - [Elon] Netflix special, sure. - What's your favorite "Rick and Morty" concept? Just to spring that on you, is there, there's a lot of sort of scientific engineering ideas explored there. There's the, - Favorite "Rick and Morty" - There's the butter robot.- Yeah, it's a great show. - You like it? - Yeah, "Rick and Morty's" Awesome. - Somebody that's exactly like you from an alternate dimension showed up there. Elon Tusk. - Yeah. That's right. - That you voiced. - Yeah, "Rick and Morty" certainly explores a lot of interesting concepts.Sure, like what's the favorite one. The butter robot certainly is, it's certainly possible to have too much sentience, in a device. You don't want to have your toaster be a super genius toaster. It's gonna hate life, 'cause all it can make is toast. It's like, you don't wanna have super-intelligence stuck in a very limited device.- Do you think it's too easy, from a, if we're talking about from the engineering perspective, super intelligence, like with Marvin, the robot. It seems like it might be very easy to engineer just a depressed robot. - Sure. - It's not obvious to engineer a robot that's going to find a fulfilling existence.Same as humans, I suppose. I wonder if that's like the default, if you don't do a good job on building a robot, it's going to be sad a lot. - Well, we can reprogram robots easier than we can reprogram humans. I guess if you let it evolve without tinkering, then it might get sad, but you can change the optimization function and have it be a cheery robot.- Like I mentioned with SpaceX, you give a lot of people hope, and a lot of people look up to you. Millions of people look up to you. If we think about young people in high school, maybe in college, what advice would you give to them about if they wanna try to do something big in this world, they wanna really have a big, positive impact, what advice would you give them about their career, maybe about life in general? - Try to be useful.Do things that are useful to your fellow human beings, to the world. It's very hard to be useful. Very hard. Are you contributing more than you consume? Try to have a positive net contribution to society. I think that's the thing to aim for. Not to try to be sort of a leader for the sake of being a leader or whatever.A lot of the time people who, a lot of times the people you want as leaders, are the people who don't want to be leaders. If you're living a useful life, that is a good life, a life worth having lived. Like I said, I would encourage people to use the mental tools of physics and apply them broadly in life.They are the best tools. - When you think about education and self-education, what do you recommend? So there's the university, there's self study. There is hands-on, sort of finding a company or a place or a set of people that do the thing you're passionate about and joining them as early as possible.There's taking a road trip across Europe for a few years and writing some poetry. Which trajectory do you suggest? In terms of learning about how you can become useful, as you mentioned, how you can have the most positive impact. - I encourage people to read a lot of books, just read, basically try to ingest as much information as you can, and try to also just develop a good general knowledge.So you at least have a rough lay of the land of the knowledge landscape, try to learn a little about a lot of things. 'Cause you might not know what you're really interested. How would you know what you're really interested in if you at least aren't like doing it? Peripheral exploration broadly of the knowledge landscape.And talk to people from different walks of life and different industries, and professions, and skills, and occupations, like just try. Learn as much as possible. Be on the search for meaning. - Isn't the whole thing a search for meaning? - Yeah, what's the meaning of life and all? But just generally, like I said, I would encourage people to read broadly in many different subject areas, and then try to find something where there's an overlap of your talents and what you're interested in.So people may be good at something, or they may have skill at a particular thing, but they don't like doing it. So you wanna try to find a thing that's a good combination of the things that you're inherently good at, but you also like doing. - And reading as a super fast shortcut to figure out which, where are you, you're both good at it, you like doing it, and it'll actually have positive impact.- Well, you gotta learn about things somehow. So reading, a broad range, just really read. More important was as a kid I read through the encyclopedia. So, that was pretty helpful. And, there was all sorts of things I didn't even know existed, well lots, obviously. - That's as broad as it gets. - Encyclopedias were suggestible, I think, whatever 40 years ago.Maybe read through like the condensed version of the Encyclopedia Britannica, I'd recommend that. You can always like skip subjects, so you read a few paragraphs and you know you're not interested, just jump to the next one. So, read the encyclopedia, or skim through it. I put a lotta stock and certainly have a lot of respect for someone who puts in an honest day's work to do useful things.And just generally to have a, not a zero sum mindset, or have more of a grow the pie mindset. When I see people like, perhaps, including some very smart people, kind of taking an attitude of, I like doing things that seem like morally questionable. It's often because they have, at a base sort of axiomatic level, a zero sum mindset.And they, without realizing it, they don't realize to have a zero sum mindset, or at least they don't realize it consciously. And so, if you have a zero sum mindset, then the only way to get ahead is by taking things from others. If the pie is fixed, then the only way to have more pie is to take someone else's pie.But this is false. Obviously the pie has grown dramatically over time, the economic pie. In reality, you can have, (Elon laughing) overuse this analogy, we can have a lot of, there's a lot of pie. (Lex laughing) My pie is not fixed. So, you really wanna make sure you're not operating, without realizing it, from a zero sum mindset.Where the only way to get ahead is to take things from others, then that's gonna result in you trying to take things from others, which is not good. It's much better to work on adding to the economic pie. Like I said, creating more than you consume. Doing more than you, yeah. So that's a big deal.I think there's a fair number of people in finance that do have a bit of a zero-sum mindset. - I mean, it's all walks of life. I've seen that. One of the reasons Rogan inspires me is he celebrates others a lot, not creating a constant competition like there's a scarcity of resources. And what happens when you celebrate others and you promote others, the ideas of others, it actually grows that pie.The resources become less scarce. And that applies in a lot of kinds of domains. It applies in academia where a lot of people are very, see some funding for academic research as zero sum. It is not, if you celebrate each other, if you make, if you get everybody to be excited about AI, about physics, about mathematics, I think there'll be more and more funding, and I think everybody wins.Yeah. That applies, I think, broadly. - Yeah, yeah. Exactly. - So the last question about love and meaning. What is the role of love in the human condition broadly, and more specific to you? How has love, romantic love or otherwise, made you a better person, a better human being? Better engineer? - Now you're asking really perplexing questions.It's hard to give a. I mean, there are many books, poems, and songs written about what is love, and what is, what exactly, what is love, baby don't hurt me. (Lex laughing) - That's one of the great ones, yes. You have earlier quoted Shakespeare, but that's really up there. - [Elon] Yeah. Love is a many splendor thing.- I mean, there's, 'cause we've talked about so many inspiring things, like be useful in the world, sort of solve problems, alleviate suffering, but it seems like connection between humans is a source, it's a source of joy, it's a source of meaning, and that's what love is, friendship, love.I just wonder if you think about that kind of thing, when you talk about preserving the light of human consciousness. - Right. - And us becoming a multi-planetary species. I mean, to me at least, that means, if we're just alone, and conscious, and intelligent, it doesn't mean nearly as much as if we're with others.Right? And there's some magic created when we're together. The friendship of it, and I think the highest form of it is love, which I think broadly is much bigger than just sort of romantic, but also yes. Romantic love and family and those kinds of things. - Well, I mean, the reason I guess I care about us becoming a multi-planet species and a space bearing civilization is foundationally, I love humanity.And so I wish to see it prosper and do great things and be happy, and if I did not love humanity, I would not care about these things. - So when you look at the whole, the human history, all of the people whose ever lived, all the people alive now, It's pretty, we're okay. On the whole, we're a pretty interesting bunch.- Yeah. All things considered, and I've read a lot of history, including the darkest, worst parts of it. Despite all that, I think on balance, I still love humanity. - You joked about it, the 42, what do you think is the meaning of this whole thing? Is there a non-numerical representation? - Oh, I should say Yeah, well really, I think what Doug Sanders was saying in "The Hitchhiker's Guide to the Galaxy" is that the universe is the answer.What we really need to figure out are what questions to ask about the answer that is the universe. And that the question is the really the hard part. And if you can properly frame the question, then the answer, relatively speaking, is easy. So therefore, if you want to understand what questions to ask about the university, you wanna understand the meaning of life, we need to expand the scope and scale of consciousness so that we're better able to understand the nature of the universe and understand the meaning of life.- And ultimately, the most important part will be to ask the right question. - [Elon] Yes. - Thereby elevating the role of the interviewer - [Elon] Yeah, exactly. - as the most important human in the room. - Good questions are, it's hard to come up with good questions. Absolutely. But yeah, that is the foundation of my philosophy is that I am curious about the nature of the universe.And obviously I will die. I don't know when I'll die, but I won't live forever. But I would like to know that we are on a path to understanding the nature of the universe and the meaning of life and what questions to ask about the answer that is the universe. And so if we expand the scope and scale of humanity, and consciousness in general, which includes silicon consciousness, then that seems like a fundamentally good thing.- Elon, like I said, I'm deeply grateful that you would spend your extremely valuable time with me today, and also that you have given millions of people hope in this difficult time, this divisive time and this cynical time. So I hope you do continue doing what you're doing. Thank you so much for talking today.- Oh, you're welcome. Thanks for your excellent questions. - Thanks for listening to this conversation with Elon Musk. To support this podcast, please check out our sponsors in the description. And now, let me leave you with some words from Elon Musk himself. "When something is important enough, you do it, even if the odds are not in your favor." Thank you for listening, and hope to see you next time.

Chris Anderson: Elon Musk, great to see you. How are you? Elon Musk: Good. How are you? CA: We're here at the Texas Gigafactory the day before this thing opens. It's been pretty crazy out there. Thank you so much for making time on a busy day. I would love you to help us, kind of, cast our minds, I don't know, 10, 20, 30 years into the future.And help us try to picture what it would take to build a future that's worth getting excited about. The last time you spoke at TED, you said that that was really just a big driver. You know, you can talk about lots of other reasons to do the work you're doing, but fundamentally, you want to think about the future and not think that it sucks.EM: Yeah, absolutely. I think in general, you know, there's a lot of discussion of like, this problem or that problem. And a lot of people are sad about the future and they're ... Pessimistic. And I think ... this is ... This is not great. I mean, we really want to wake up in the morning and look forward to the future.We want to be excited about what's going to happen. And life cannot simply be about sort of, solving one miserable problem after another. CA: So if you look forward 30 years, you know, the year 2050 has been labeled by scientists as this, kind of, almost like this doomsday deadline on climate. There's a consensus of scientists, a large consensus of scientists, who believe that if we haven't completely eliminated greenhouse gases or offset them completely by 2050, effectively we're inviting climate catastrophe.Do you believe there is a pathway to avoid that catastrophe? And what would it look like? EM: Yeah, so I am not one of the doomsday people, which may surprise you. I actually think we're on a good path. But at the same time, I want to caution against complacency. So, so long as we are not complacent, as long as we have a high sense of urgency about moving towards a sustainable energy economy, then I think things will be fine.So I can't emphasize that enough, as long as we push hard and are not complacent, the future is going to be great. Don't worry about it. I mean, worry about it, but if you worry about it, ironically, it will be a self-unfulfilling prophecy. So, like, there are three elements to a sustainable energy future.One is of sustainable energy generation, which is primarily wind and solar. There's also hydro, geothermal, I'm actually pro-nuclear. I think nuclear is fine. But it's going to be primarily solar and wind, as the primary generators of energy. The second part is you need batteries to store the solar and wind energy because the sun doesn't shine all the time, the wind doesn't blow all the time.So it's a lot of stationary battery packs. And then you need electric transport. So electric cars, electric planes, boats. And then ultimately, it’s not really possible to make electric rockets, but you can make the propellant used in rockets using sustainable energy. So ultimately, we can have a fully sustainable energy economy.And it's those three things: solar/wind, stationary battery pack, electric vehicles. So then what are the limiting factors on progress? The limiting factor really will be battery cell production. So that's going to really be the fundamental rate driver. And then whatever the slowest element of the whole lithium-ion battery cells supply chain, from mining and the many steps of refining to ultimately creating a battery cell and putting it into a pack, that will be the limiting factor on progress towards sustainability.CA: All right, so we need to talk more about batteries, because the key thing that I want to understand, like, there seems to be a scaling issue here that is kind of amazing and alarming. You have said that you have calculated that the amount of battery production that the world needs for sustainability is 300 terawatt hours of batteries.That's the end goal? EM: Very rough numbers, and I certainly would invite others to check our calculations because they may arrive at different conclusions. But in order to transition, not just current electricity production, but also heating and transport, which roughly triples the amount of electricity that you need, it amounts to approximately 300 terawatt hours of installed capacity.CA: So we need to give people a sense of how big a task that is. I mean, here we are at the Gigafactory. You know, this is one of the biggest buildings in the world. What I've read, and tell me if this is still right, is that the goal here is to eventually produce 100 gigawatt hours of batteries here a year eventually.EM: We will probably do more than that, but yes, hopefully we get there within a couple of years. CA: Right. But I mean, that is one -- EM: 0.1 terrawat hours. CA: But that's still 1/100 of what's needed. How much of the rest of that 100 is Tesla planning to take on let's say, between now and 2030, 2040, when we really need to see the scale up happen? EM: I mean, these are just guesses.So please, people shouldn't hold me to these things. It's not like this is like some -- What tends to happen is I'll make some like, you know, best guess and then people, in five years, there’ll be some jerk that writes an article: "Elon said this would happen, and it didn't happen. He's a liar and a fool." It's very annoying when that happens. So these are just guesses, this is a conversation. CA: Right. EM: I think Tesla probably ends up doing 10 percent of that. Roughly. CA: Let's say 2050 we have this amazing, you know, 100 percent sustainable electric grid made up of, you know, some mixture of the sustainable energy sources you talked about.That same grid probably is offering the world really low-cost energy, isn't it, compared with now. And I'm curious about like, are people entitled to get a little bit excited about the possibilities of that world? EM: People should be optimistic about the future. Humanity will solve sustainable energy.It will happen if we, you know, continue to push hard, the future is bright and good from an energy standpoint. And then it will be possible to also use that energy to do carbon sequestration. It takes a lot of energy to pull carbon out of the atmosphere because in putting it in the atmosphere it releases energy.So now, you know, obviously in order to pull it out, you need to use a lot of energy. But if you've got a lot of sustainable energy from wind and solar, you can actually sequester carbon. So you can reverse the CO2 parts per million of the atmosphere and oceans. And also you can really have as much fresh water as you want.Earth is mostly water. We should call Earth “Water.” It's 70 percent water by surface area. Now most of that’s seawater, but it's like we just happen to be on the bit that's land. CA: And with energy, you can turn seawater into -- EM: Yes. CA: Irrigating water or whatever water you need. EM: At very low cost.Things will be good. CA: Things will be good. And also, there's other benefits to this non-fossil fuel world where the air is cleaner -- EM: Yes, exactly. Because, like, when you burn fossil fuels, there's all these side reactions and toxic gases of various kinds. And sort of little particulates that are bad for your lungs.Like, there's all sorts of bad things that are happening that will go away. And the sky will be cleaner and quieter. The future's going to be good. CA: I want us to switch now to think a bit about artificial intelligence. But the segue there, you mentioned how annoying it is when people call you up for bad predictions in the past.So I'm possibly going to be annoying now, but I’m curious about your timelines and how you predict and how come some things are so amazingly on the money and some aren't. So when it comes to predicting sales of Tesla vehicles, for example, you've kind of been amazing, I think in 2014 when Tesla had sold that year 60,000 cars, you said, "2020, I think we will do half a million a year." EM: Yeah, we did almost exactly a half million. CA: You did almost exactly half a million. You were scoffed in 2014 because no one since Henry Ford, with the Model T, had come close to that kind of growth rate for cars. You were scoffed, and you actually hit 500,000 cars and then 510,000 or whatever produced.But five years ago, last time you came to TED, I asked you about full self-driving, and you said, “Yeah, this very year, I'm confident that we will have a car going from LA to New York without any intervention." EM: Yeah, I don't want to blow your mind, but I'm not always right. CA: (Laughs) What's the difference between those two? Why has full self-driving in particular been so hard to predict? EM: I mean, the thing that really got me, and I think it's going to get a lot of other people,is that there are just so many false dawns with self-driving, where you think you've got the problem, have a handle on the problem, and then it, no, turns out you just hit a ceiling. Because if you were to plot the progress, the progress looks like a log curve. So it's like a series of log curves. So most people don't know what a log curve is, I suppose.CA: Show the shape with your hands. EM: It goes up you know, sort of a fairly straight way, and then it starts tailing off and you start getting diminishing returns. And you're like, uh oh, it was trending up and now it's sort of, curving over and you start getting to these, what I call local maxima, where you don't realize basically how dumb you were.And then it happens again. And ultimately... These things, you know, in retrospect, they seem obvious, but in order to solve full self-driving properly, you actually have to solve real-world AI. Because what are the road networks designed to work with? They're designed to work with a biological neural net, our brains, and with vision, our eyes.And so in order to make it work with computers, you basically need to solve real-world AI and vision. Because we need cameras and silicon neural nets in order to have self-driving work for a system that was designed for eyes and biological neural nets. You know, I guess when you put it that way, it's sort of, like, quite obvious that the only way to solve full self-driving is to solve real world AI and sophisticated vision.CA: What do you feel about the current architecture? Do you think you have an architecture now where there is a chance for the logarithmic curve not to tail off any anytime soon? EM: Well I mean, admittedly these may be infamous last words, but I actually am confident that we will solve it this year. That we will exceed -- The probability of an accident, at what point do you exceed that of the average person? I think we will exceed that this year.CA: What are you seeing behind the scenes that gives you that confidence? EM: We’re almost at the point where we have a high-quality unified vector space. In the beginning, we were trying to do this with image recognition on individual images. But if you get one image out of a video, it's actually quite hard to see what's going on without ambiguity.But if you look at a video segment of a few seconds of video, that ambiguity resolves. So the first thing we had to do is tie all eight cameras together so they're synchronized, so that all the frames are looked at simultaneously and labeled simultaneously by one person, because we still need human labeling.So at least they’re not labeled at different times by different people in different ways. So it's sort of a surround picture. Then a very important part is to add the time dimension. So that you’re looking at surround video, and you're labeling surround video. And this is actually quite difficult to do from a software standpoint.We had to write our own labeling tools and then create auto labeling, create auto labeling software to amplify the efficiency of human labelers because it’s quite hard to label. In the beginning, it was taking several hours to label a 10-second video clip. This is not scalable. So basically what you have to have is you have to have surround video, and that surround video has to be primarily automatically labeled with humans just being editors and making slight corrections to the labeling of the video and then feeding back those corrections into the future auto labeler,so you get this flywheel eventually where the auto labeler is able to take in vast amounts of video and with high accuracy, automatically label the video for cars, lane lines, drive space. CA: What you’re saying is ... the result of this is that you're effectively giving the car a 3D model of the actual objects that are all around it.It knows what they are, and it knows how fast they are moving. And the remaining task is to predict what the quirky behaviors are that, you know, that when a pedestrian is walking down the road with a smaller pedestrian, that maybe that smaller pedestrian might do something unpredictable or things like that.You have to build into it before you can really call it safe. EM: You basically need to have memory across time and space. So what I mean by that is ... Memory can’t be infinite, because it's using up a lot of the computer's RAM basically. So you have to say how much are you going to try to remember? It's very common for things to be occluded.So if you talk about say, a pedestrian walking past a truck where you saw the pedestrian start on one side of the truck, then they're occluded by the truck. You would know intuitively, OK, that pedestrian is going to pop out the other side, most likely. CA: A computer doesn't know it. EM: You need to slow down.CA: A skeptic is going to say that every year for the last five years, you've kind of said, well, no this is the year, we're confident that it will be there in a year or two or, you know, like it's always been about that far away. But we've got a new architecture now, you're seeing enough improvement behind the scenes to make you not certain, but pretty confident, that, by the end of this year, what in most, not in every city, and every circumstance but in many cities and circumstances,basically the car will be able to drive without interventions safer than a human. EM: Yes. I mean, the car currently drives me around Austin most of the time with no interventions. So it's not like ... And we have over 100,000 people in our full self-driving beta program. So you can look at the videos that they post online.CA: I do. And some of them are great, and some of them are a little terrifying. I mean, occasionally the car seems to veer off and scare the hell out of people. EM: It’s still a beta. CA: But you’re behind the scenes, looking at the data, you're seeing enough improvement to believe that a this-year timeline is real.EM: Yes, that's what it seems like. I mean, we could be here talking again in a year, like, well, another year went by, and it didn’t happen. But I think this is the year. CA: And so in general, when people talk about Elon time, I mean it sounds like you can't just have a general rule that if you predict that something will be done in six months, actually what we should imagine is it’s going to be a year or it’s like two-x or three-x, it depends on the type of prediction.Some things, I guess, things involving software, AI, whatever, are fundamentally harder to predict than others. Is there an element that you actually deliberately make aggressive prediction timelines to drive people to be ambitious? Without that, nothing gets done? EM: Well, I generally believe, in terms of internal timelines, that we want to set the most aggressive timeline that we can.Because there’s sort of like a law of gaseous expansion where, for schedules, where whatever time you set, it's not going to be less than that. It's very rare that it'll be less than that. But as far as our predictions are concerned, what tends to happen in the media is that they will report all the wrong ones and ignore all the right ones.Or, you know, when writing an article about me -- I've had a long career in multiple industries. If you list my sins, I sound like the worst person on Earth. But if you put those against the things I've done right, it makes much more sense, you know? So essentially like, the longer you do anything, the more mistakes that you will make cumulatively.Which, if you sum up those mistakes, will sound like I'm the worst predictor ever. But for example, for Tesla vehicle growth, I said I think we’d do 50 percent, and we’ve done 80 percent. CA: Yes. EM: But they don't mention that one. So, I mean, I'm not sure what my exact track record is on predictions.They're more optimistic than pessimistic, but they're not all optimistic. Some of them are exceeded probably more or later, but they do come true. It's very rare that they do not come true. It's sort of like, you know, if there's some radical technology prediction, the point is not that it was a few years late, but that it happened at all.That's the more important part. CA: So it feels like at some point in the last year, seeing the progress on understanding, the Tesla AI understanding the world around it, led to a kind of, an aha moment at Tesla. Because you really surprised people recently when you said probably the most important product development going on at Tesla this year is this robot, Optimus.EM: Yes. CA: Many companies out there have tried to put out these robots, they've been working on them for years. And so far no one has really cracked it. There's no mass adoption robot in people's homes. There are some in manufacturing, but I would say, no one's kind of, really cracked it.Is it something that happened in the development of full self-driving that gave you the confidence to say, "You know what, we could do something special here." EM: Yeah, exactly. So, you know, it took me a while to sort of realize that in order to solve self-driving, you really needed to solve real-world AI.And at the point of which you solve real-world AI for a car, which is really a robot on four wheels, you can then generalize that to a robot on legs as well. The two hard parts I think -- like obviously companies like Boston Dynamics have shown that it's possible to make quite compelling, sometimes alarming robots.CA: Right. EM: You know, so from a sensors and actuators standpoint, it's certainly been demonstrated by many that it's possible to make a humanoid robot. The things that are currently missing are enough intelligence for the robot to navigate the real world and do useful things without being explicitly instructed.So the missing things are basically real-world intelligence and scaling up manufacturing. Those are two things that Tesla is very good at. And so then we basically just need to design the specialized actuators and sensors that are needed for humanoid robot. People have no idea, this is going to be bigger than the car.CA: So let's dig into exactly that. I mean, in one way, it's actually an easier problem than full self-driving because instead of an object going along at 60 miles an hour, which if it gets it wrong, someone will die. This is an object that's engineered to only go at what, three or four or five miles an hour.And so a mistake, there aren't lives at stake. There might be embarrassment at stake. EM: So long as the AI doesn't take it over and murder us in our sleep or something. CA: Right. (Laughter) So talk about -- I think the first applications you've mentioned are probably going to be manufacturing, but eventually the vision is to have these available for people at home.If you had a robot that really understood the 3D architecture of your house and knew where every object in that house was or was supposed to be, and could recognize all those objects, I mean, that’s kind of amazing, isn’t it? Like the kind of thing that you could ask a robot to do would be what? Like, tidy up? EM: Yeah, absolutely.Make dinner, I guess, mow the lawn. CA: Take a cup of tea to grandma and show her family pictures. EM: Exactly. Take care of my grandmother and make sure -- CA: It could obviously recognize everyone in the home. It could play catch with your kids. EM: Yes. I mean, obviously, we need to be careful this doesn't become a dystopian situation.I think one of the things that's going to be important is to have a localized ROM chip on the robot that cannot be updated over the air. Where if you, for example, were to say, “Stop, stop, stop,” if anyone said that, then the robot would stop, you know, type of thing. And that's not updatable remotely.I think it's going to be important to have safety features like that. CA: Yeah, that sounds wise. EM: And I do think there should be a regulatory agency for AI. I've said that for many years. I don't love being regulated, but I think this is an important thing for public safety. CA: Let's come back to that.But I don't think many people have really sort of taken seriously the notion of, you know, a robot at home. I mean, at the start of the computing revolution, Bill Gates said there's going to be a computer in every home. And people at the time said, yeah, whatever, who would even want that. Do you think there will be basically like in, say, 2050 or whatever, like a robot in most homes, is what there will be, and people will love them and count on them? You’ll have your own butler basically.EM: Yeah, you'll have your sort of buddy robot probably, yeah. CA: I mean, how much of a buddy? How many applications have you thought, you know, can you have a romantic partner, a sex partner? EM: It's probably inevitable. I mean, I did promise the internet that I’d make catgirls. We could make a robot catgirl.CA: Be careful what you promise the internet. (Laughter) EM: So, yeah, I guess it'll be whatever people want really, you know. CA: What sort of timeline should we be thinking about of the first models that are actually made and sold? EM: Well, you know, the first units that we intend to make are for jobs that are dangerous, boring, repetitive, and things that people don't want to do.And, you know, I think we’ll have like an interesting prototype sometime this year. We might have something useful next year, but I think quite likely within at least two years. And then we'll see rapid growth year over year of the usefulness of the humanoid robots and decrease in cost and scaling up production.CA: Initially just selling to businesses, or when do you picture you'll start selling them where you can buy your parents one for Christmas or something? EM: I'd say in less than ten years. CA: Help me on the economics of this. So what do you picture the cost of one of these being? EM: Well, I think the cost is actually not going to be crazy high.Like less than a car. Initially, things will be expensive because it'll be a new technology at low production volume. The complexity and cost of a car is greater than that of a humanoid robot. So I would expect that it's going to be less than a car, or at least equivalent to a cheap car. CA: So even if it starts at 50k, within a few years, it’s down to 20k or lower or whatever.And maybe for home they'll get much cheaper still. But think about the economics of this. If you can replace a $30,000, $40,000-a-year worker, which you have to pay every year, with a one-time payment of $25,000 for a robot that can work longer hours, a pretty rapid replacement of certain types of jobs.How worried should the world be about that? EM: I wouldn't worry about the sort of, putting people out of a job thing. I think we're actually going to have, and already do have, a massive shortage of labor. So I think we will have ... Not people out of work, but actually still a shortage labor even in the future.But this really will be a world of abundance. Any goods and services will be available to anyone who wants them. It'll be so cheap to have goods and services, it will be ridiculous. CA: I'm presuming it should be possible to imagine a bunch of goods and services that can't profitably be made now but could be made in that world, courtesy of legions of robots.EM: Yeah. It will be a world of abundance. The only scarcity that will exist in the future is that which we decide to create ourselves as humans. CA: OK. So AI is allowing us to imagine a differently powered economy that will create this abundance. What are you most worried about going wrong? EM: Well, like I said, AI and robotics will bring out what might be termed the age of abundance.Other people have used this word, and that this is my prediction: it will be an age of abundance  for everyone. But I guess there’s ... The dangers would be the artificial general intelligence or digital superintelligence decouples from a collective human will and goes in the direction that for some reason we don't like.Whatever direction it might go. You know, that’s sort of the idea behind Neuralink, is to try to more tightly couple collective human world to digital superintelligence. And also along the way solve a lot of brain injuries and spinal injuries and that kind of thing. So even if it doesn't succeed in the greater goal, I think it will succeed in the goal of alleviating brain and spine damage.CA: So the spirit there is that if we're going to make these AIs that are so vastly intelligent, we ought to be wired directly to them so that we ourselves can have those superpowers more directly. But that doesn't seem to avoid the risk that those superpowers might ... turn ugly in unintended ways.EM: I think it's a risk, I agree. I'm not saying that I have some certain answer to that risk. I’m just saying like maybe one of the things that would be good for ensuring that the future is one that we want is to more tightly couple the collective human world to digital intelligence. The issue that we face here is that we are already a cyborg, if you think about it.The computers are an extension of ourselves. And when we die, we have, like, a digital ghost. You know, all of our text messages and social media, emails. And it's quite eerie actually, when someone dies but everything online is still there. But you say like, what's the limitation? What is it that inhibits a human-machine symbiosis? It's the data rate.When you communicate, especially with a phone, you're moving your thumbs very slowly. So you're like moving your two little meat sticks at a rate that’s maybe 10 bits per second, optimistically, 100 bits per second. And computers are communicating at the gigabyte level and beyond. CA: Have you seen evidence that the technology is actually working, that you've got a richer, sort of, higher bandwidth connection, if you like, between like external electronics and a brain than has been possible before?EM: Yeah. I mean, the fundamental principles of reading neurons, sort of doing read-write on neurons with tiny electrodes, have been demonstrated for decades. So it's not like the concept is new. The problem is that there is no product that works well that you can go and buy. So it's all sort of, in research labs.And it's like some cords sticking out of your head. And it's quite gruesome, and it's really ... There's no good product that actually does a good job and is high-bandwidth and safe and something actually that you could buy and would want to buy. But the way to think of the Neuralink device is kind of like a Fitbit or an Apple Watch.That's where we take out sort of a small section of skull about the size of a quarter, replace that with what, in many ways really is very much like a Fitbit, Apple Watch or some kind of smart watch thing. But with tiny, tiny wires, very, very tiny wires. Wires so tiny, it’s hard to even see them. And it's very important to have very tiny wires so that when they’re implanted, they don’t damage the brain.CA: How far are you from putting these into humans? EM: Well, we have put in our FDA application to aspirationally do the first human implant this year. CA: The first uses will be for neurological injuries of different kinds. But rolling the clock forward and imagining when people are actually using these for their own enhancement, let's say, and for the enhancement of the world, how clear are you in your mind as to what it will feel like to have one of these inside your head? EM: Well, I do want to emphasize we're at an early stage.And so it really will be many years before we have anything approximating a high-bandwidth neural interface that allows for AI-human symbiosis. For many years, we will just be solving brain injuries and spinal injuries. For probably a decade. This is not something that will suddenly one day it will have this incredible sort of whole brain interface.It's going to be, like I said, at least a decade of really just solving brain injuries and spinal injuries. And really, I think you can solve a very wide range of brain injuries, including severe depression, morbid obesity, sleep, potentially schizophrenia, like, a lot of things that cause great stress to people.Restoring memory in older people. CA: If you can pull that off, that's the app I will sign up for. EM: Absolutely. CA: Please hurry. (Laughs) EM: I mean, the emails that we get at Neuralink are heartbreaking. I mean, they'll send us just tragic, you know, where someone was sort of, in the prime of life and they had an accident on a motorcycle and someone who's 25, you know, can't even feed themselves.And this is something we could fix. CA: But you have said that AI is one of the things you're most worried about and that Neuralink may be one of the ways where we can keep abreast of it. EM: Yeah, there's the short-term thing, which I think is helpful on an individual human level with injuries. And then the long-term thing is an attempt to address the civilizational risk of AI by bringing digital intelligence and biological intelligence closer together.I mean, if you think of how the brain works today, there are really two layers to the brain. There's the limbic system and the cortex. You've got the kind of, animal brain where -- it’s kind of like the fun part, really. CA: It's where most of Twitter operates, by the way. EM: I think Tim Urban said, we’re like somebody, you know, stuck a computer on a monkey.You know, so we're like, if you gave a monkey a computer, that's our cortex. But we still have a lot of monkey instincts. Which we then try to rationalize as, no, it's not a monkey instinct. It’s something more important than that. But it's often just really a monkey instinct. We're just monkeys with a computer stuck in our brain.But even though the cortex is sort of the smart, or the intelligent part of the brain, the thinking part of the brain, I've not yet met anyone who wants to delete their limbic system or their cortex. They're quite happy having both. Everyone wants both parts of their brain. And people really want their phones and their computers, which are really the tertiary, the third part of your intelligence.It's just that it's ... Like the bandwidth, the rate of communication with that tertiary layer is slow. And it's just a very tiny straw to this tertiary layer. And we want to make that tiny straw a big highway. And I’m definitely not saying that this is going to solve everything. Or this is you know, it’s the only thing -- it’s something that might be helpful.And worst-case scenario, I think we solve some important brain injury, spinal injury issues, and that's still a great outcome. CA: Best-case scenario, we may discover new human possibility, telepathy, you've spoken of, in a way, a connection with a loved one, you know, full memory and much faster thought processing maybe.All these things. It's very cool. If AI were to take down Earth, we need a plan B. Let's shift our attention to space. We spoke last time at TED about reusability, and you had just demonstrated that spectacularly for the first time. Since then, you've gone on to build this monster rocket, Starship, which kind of changes the rules of the game in spectacular ways.Tell us about Starship. EM: Starship is extremely fundamental. So the holy grail of rocketry or space transport is full and rapid reusability. This has never been achieved. The closest that anything has come is our Falcon 9 rocket, where we are able to recover the first stage, the boost stage, which is probably about 60 percent of the cost of the vehicle of the whole launch, maybe 70 percent.And we've now done that over a hundred times. So with Starship, we will be recovering the entire thing. Or at least that's the goal. CA: Right. EM: And moreover, recovering it in such a way that it can be immediately re-flown. Whereas with Falcon 9, we still need to do some amount of refurbishment to the booster and to the fairing nose cone.But with Starship, the design goal is immediate re-flight. So you just refill propellants and go again. And this is gigantic. Just as it would be in any other mode of transport. CA: And the main design is to basically take 100 plus people at a time, plus a bunch of things that they need, to Mars. So, first of all, talk about that piece.What is your latest timeline? One, for the first time, a Starship goes to Mars, presumably without people, but just equipment. Two, with people. Three, there’s sort of, OK, 100 people at a time, let's go. EM: Sure. And just to put the cost thing into perspective, the expected cost of Starship, putting 100 tons into orbit, is significantly less than what it would have cost or what it did cost to put our tiny Falcon 1 rocket into orbit.Just as the cost of flying a 747 around the world is less than the cost of a small airplane. You know, a small airplane that was thrown away. So it's really pretty mind-boggling that the giant thing costs less, way less than the small thing. So it doesn't use exotic propellants or things that are difficult to obtain on Mars.It uses methane as fuel, and it's primarily oxygen, roughly 77-78 percent oxygen by weight. And Mars has a CO2 atmosphere and has water ice, which is CO2 plus H2O, so you can make CH4, methane, and O2, oxygen, on Mars. CA: Presumably, one of the first tasks on Mars will be to create a fuel plant that can create the fuel for the return trips of many Starships.EM: Yes. And actually, it's mostly going to be oxygen plants, because it's 78 percent oxygen, 22 percent fuel. But the fuel is a simple fuel that is easy to create on Mars. And in many other parts of the solar system. So basically ... And it's all propulsive landing, no parachutes, nothing thrown away.It has a heat shield that’s capable of entering on Earth or Mars. We can even potentially go to Venus. but you don't want to go there. (Laughs) Venus is hell, almost literally. But you could ... It's a generalized method of transport to anywhere in the solar system, because the point at which you have propellant depo on Mars, you can then travel to the asteroid belt and to the moons of Jupiter and Saturn and ultimately anywhere in the solar system.CA: But your main focus and SpaceX's main focus is still Mars. That is the mission. That is where most of the effort will go? Or are you actually imagining a much broader array of uses even in the coming, you know, the first decade or so of uses of this. Where we could go, for example, to other places in the solar system to explore, perhaps NASA wants to use the rocket for that reason.EM: Yeah, NASA is planning to use a Starship to return to the moon, to return people to the moon. And so we're very honored that NASA has chosen us to do this. But I'm saying it is a generalized -- it’s a general solution to getting anywhere in the greater solar system. It's not suitable for going to another star system, but it is a general solution for transport anywhere in the solar system.CA: Before it can do any of that, it's got to demonstrate it can get into orbit, you know, around Earth. What’s your latest advice on the timeline for that? EM: It's looking promising for us to have an orbital launch attempt in a few months. So we're actually integrating -- will be integrating the engines into the booster for the first orbital flight starting in about a week or two.And the launch complex itself is ready to go. So assuming we get regulatory approval, I think we could have an orbital launch attempt within a few months. CA: And a radical new technology like this presumably there is real risk on those early attempts. EM: Oh, 100 percent, yeah. The joke I make all the time is that excitement is guaranteed.Success is not guaranteed, but excitement certainly is. CA: But the last I saw on your timeline, you've slightly put back the expected date to put the first human on Mars till 2029, I want to say? EM: Yeah, I mean, so let's see. I mean, we have built a production system for Starship, so we're making a lot of ships and boosters.CA: How many are you planning to make actually? EM: Well, we're currently expecting to make a booster and a ship roughly every, well, initially, roughly every couple of months, and then hopefully by the end of this year, one every month. So it's giant rockets, and a lot of them. Just talking in terms of rough orders of magnitude, in order to create a self-sustaining city on Mars, I think you will need something on the order of a thousand ships.And we just need a Helen of Sparta, I guess, on Mars. CA: This is not in most people's heads, Elon. EM: The planet that launched 1,000 ships. CA: That's nice. But this is not in most people's heads, this picture that you have in your mind. There's basically a two-year window, you can only really fly to Mars conveniently every two years.You were picturing that during the 2030s, every couple of years, something like 1,000 Starships take off, each containing 100 or more people. That picture is just completely mind-blowing to me. That sense of this armada of humans going to -- EM: It'll be like "Battlestar Galactica," the fleet departs.CA: And you think that it can basically be funded by people spending maybe a couple hundred grand on a ticket to Mars? Is that price about where it has been? EM: Well, I think if you say like, what's required in order to get enough people and enough cargo to Mars to build a self-sustaining city. And it's where you have an intersection of sets of people who want to go, because I think only a small percentage of humanity will want to go, and can afford to go or get sponsorship in some manner.That intersection of sets, I think, needs to be a million people or something like that. And so it’s what can a million people afford, or get sponsorship for, because I think governments will also pay for it, and people can take out loans. But I think at the point at which you say, OK, like, if moving to Mars costs are, for argument’s sake, $100,000, then I think you know, almost anyone can work and save up and eventually have $100,000 and be able to go to Mars if they want.We want to make it available to anyone who wants to go. It's very important to emphasize that Mars, especially in the beginning, will not be luxurious. It will be dangerous, cramped, difficult, hard work. It's kind of like that Shackleton ad for going to the Antarctic, which I think is actually not real, but it sounds real and it's cool.It's sort of like, the sales pitch for going to Mars is, "It's dangerous, it's cramped. You might not make it back. It's difficult, it's hard work." That's the sales pitch. CA: Right. But you will make history. EM: But it'll be glorious. CA: So on that kind of launch rate you're talking about over two decades, you could get your million people to Mars, essentially.Whose city is it? Is it NASA's city, is it SpaceX's city? EM: It’s the people of Mars’ city. The reason for this, I mean, I feel like why do this thing? I think this is important for maximizing the probable lifespan of humanity or consciousness. Human civilization could come to an end for external reasons, like a giant meteor or super volcanoes or extreme climate change.Or World War III, or you know, any one of a number of reasons. But the probable life span of civilizational consciousness as we know it, which we should really view as this very delicate thing, like a small candle in a vast darkness. That is what appears to be the case. We're in this vast darkness of space, and there's this little candle of consciousness that’s only really come about after 4.5 billion years, and it could just go out. CA: I think that's powerful, and I think a lot of people will be inspired by that vision. And the reason you need the million people is because there has to be enough people there to do everything that you need to survive. EM: Really, like the critical threshold is if the ships from Earth stop coming for any reason, does the Mars City die out or not? And so we have to -- You know, people talk about like, the sort of, the great filters, the things that perhaps, you know, we talk about the Fermi paradox, and where are the aliens?Well maybe there are these various great filters that the aliens didn’t pass, and so they eventually just ceased to exist. And one of the great filters is becoming a multi-planet species. So we want to pass that filter. And I'll be long-dead before this is, you know, a real thing, before it happens. But I’d like to at least see us make great progress in this direction.CA: Given how tortured the Earth is right now, how much we're beating each other up, shouldn't there be discussions going on with everyone who is dreaming about Mars to try to say, we've got a once in a civilization's chance to make some new rules here? Should someone be trying to lead those discussions to figure out what it means for this to be the people of Mars' City? EM: Well, I think ultimately this will be up to the people of Mars to decide how they want to rethink society.Yeah there’s certainly risk there. And hopefully the people of Mars will be more enlightened and will not fight amongst each other too much. I mean, I have some recommendations, which people of Mars may choose to listen to or not. I would advocate for more of a direct democracy, not a representative democracy, and laws that are short enough for people to understand.Where it is harder to create laws than to get rid of them. CA: Coming back a bit nearer term, I'd love you to just talk a bit about some of the other possibility space that Starship seems to have created. So given -- Suddenly we've got this ability to move 100 tons-plus into orbit. So we've just launched the James Webb telescope, which is an incredible thing.It's unbelievable. EM: Exquisite piece of technology. CA: Exquisite piece of technology. But people spent two years trying to figure out how to fold up this thing. It's a three-ton telescope. EM: We can make it a lot easier if you’ve got more volume and mass. CA: But let's ask a different question.Which is, how much more powerful a telescope could someone design based on using Starship, for example? EM: I mean, roughly, I'd say it's probably an order of magnitude more resolution. If you've got 100 tons and a thousand cubic meters volume, which is roughly what we have. CA: And what about other exploration through the solar system? I mean, I'm you know -- EM: Europa is a big question mark.CA: Right, so there's an ocean there. And what you really want to do is to drop a submarine into that ocean. EM: Maybe there's like, some squid civilization, cephalopod civilization under the ice of Europa. That would be pretty interesting. CA: I mean, Elon, if you could take a submarine to Europa and we see pictures of this thing being devoured by a squid, that would honestly be the happiest moment of my life.EM: Pretty wild, yeah. CA: What other possibilities are out there? Like, it feels like if you're going to create a thousand of these things, they can only fly to Mars every two years. What are they doing the rest of the time? It feels like there's this explosion of possibility that I don't think people are really thinking about.EM: I don't know, we've certainly got a long way to go. As you alluded to earlier, we still have to get to orbit. And then after we get to orbit, we have to really prove out and refine full and rapid reusability. That'll take a moment. But I do think we will solve this. I'm highly confident we will solve this at this point.CA: Do you ever wake up with the fear that there's going to be this Hindenburg moment for SpaceX where ... EM: We've had many Hindenburg. Well, we've never had Hindenburg moments with people, which is very important. Big difference. We've blown up quite a few rockets. So there's a whole compilation online that we put together and others put together, it's showing rockets are hard.I mean, the sheer amount of energy going through a rocket boggles the mind. So, you know, getting out of Earth's gravity well is difficult. We have a strong gravity and a thick atmosphere. And Mars, which is less than 40 percent, it's like, 37 percent of Earth's gravity and has a thin atmosphere.The ship alone can go all the way from the surface of Mars to the surface of Earth. Whereas getting to Mars requires a giant booster and orbital refilling. CA: So, Elon, as I think more about this incredible array of things that you're involved with, I keep seeing these synergies, to use a horrible word, between them.You know, for example, the robots you're building from Tesla could possibly be pretty handy on Mars, doing some of the dangerous work and so forth. I mean, maybe there's a scenario where your city on Mars doesn't need a million people, it needs half a million people and half a million robots. And that's a possibility.Maybe The Boring Company could play a role helping create some of the subterranean dwelling spaces that you might need. EM: Yeah. CA: Back on planet Earth, it seems like a partnership between Boring Company and Tesla could offer an unbelievable deal to a city to say, we will create for you a 3D network of tunnels populated by robo-taxis that will offer fast, low-cost transport to anyone.You know, full self-driving may or may not be done this year. And in some cities, like, somewhere like Mumbai, I suspect won't be done for a decade. EM: Some places are more challenging than others. CA: But today, today, with what you've got, you could put a 3D network of tunnels under there. EM: Oh, if it’s just in a tunnel, that’s a solved problem.CA: Exactly, full self-driving is a solved problem. To me, there’s amazing synergy there. With Starship, you know, Gwynne Shotwell talked about by 2028 having from city to city, you know, transport on planet Earth. EM: This is a real possibility. The fastest way to get from one place to another, if it's a long distance, is a rocket.It's basically an ICBM. CA: But it has to land -- Because it's an ICBM, it has to land probably offshore, because it's loud. So why not have a tunnel that then connects to the city with Tesla? And Neuralink. I mean, if you going to go to Mars having a telepathic connection with loved ones back home, even if there's a time delay...EM: These are not intended to be connected, by the way. But there certainly could be some synergies, yeah. CA: Surely there is a growing argument that you should actually put all these things together into one company and just have a company devoted to creating a future that’s exciting, and let a thousand flowers bloom.Have you been thinking about that? EM: I mean, it is tricky because Tesla is a publicly-traded company, and the investor base of Tesla and SpaceX and certainly Boring Company and Neuralink are quite different. Boring Company and Neuralink are tiny companies. CA: By comparison. EM: Yeah, Tesla's got 110,000 people.SpaceX I think is around 12,000 people. Boring Company and Neuralink are both under 200 people. So they're little, tiny companies, but they will probably get bigger in the future. They will get bigger in the future. It's not that easy to sort of combine these things. CA: Traditionally, you have said that for SpaceX especially, you wouldn't want it public, because public investors wouldn't support the craziness of the idea of going to Mars or whatever.EM: Yeah, making life multi-planetary is outside of the normal time horizon of Wall Street analysts. (Laughs) To say the least. CA: I think something's changed, though. What's changed is that Tesla is now so powerful and so big and throws off so much cash that you actually could connect the dots here.Just tell the public that x-billion dollars a year, whatever your number is, will be diverted to the Mars mission. I suspect you'd have massive interest in that company. And it might unlock a lot more possibility for you, no? EM: I would like to give the public access to ownership of SpaceX, but I mean the thing that like, the overhead associated with a public company is high.I mean, as a public company, you're just constantly sued. It does occupy like, a fair bit of ... You know, time and effort to deal with these things. CA: But you would still only have one public company, it would be bigger, and have more things going on. But instead of being on four boards, you'd be on one.EM: I'm actually not even on the Neuralink or Boring Company boards. And I don't really attend the SpaceX board meetings. We only have two a year, and I just stop by and chat for an hour. The board overhead for a public company is much higher. CA: I think some investors probably worry about how your time is being split, and they might be excited by you know, that.Anyway, I just woke up the other day thinking, just, there are so many ways in which these things connect. And you know, just the simplicity of that mission, of building a future that is worth getting excited about, might appeal to an awful lot of people. Elon, you are reported by Forbes and everyone else as now, you know, the world's richest person.EM: That’s not a sovereign. CA: (Laughs) EM: You know, I think it’s fair to say that if somebody is like, the king or de facto king of a country, they're wealthier than I am. CA: But it’s just harder to measure -- So $300 billion. I mean, your net worth on any given day is rising or falling by several billion dollars.How insane is that? EM: It's bonkers, yeah. CA: I mean, how do you handle that psychologically? There aren't many people in the world who have to even think about that. EM: I actually don't think about that too much. But the thing that is actually more difficult and that does make sleeping difficult is that, you know, every good hour or even minute of thinking about Tesla and SpaceX has such a big effect on the company that I really try to work as much as possible, you know, to the edge of sanity, basically.Because you know, Tesla’s getting to the point where probably will get to the point later this year, where every high-quality minute of thinking is a million dollars impact on Tesla. Which is insane. I mean, the basic, you know, if Tesla is doing, you know, sort of $2 billion a week, let’s say, in revenue, it’s sort of $300 million a day, seven days a week.You know, it's ... CA: If you can change that by five percent in an hour’s brainstorm, that's a pretty valuable hour. EM: I mean, there are many instances where a half-hour meeting, I was able to improve the financial outcome of the company by $100 million in a half-hour meeting. CA: There are many other people out there who can't stand this world of billionaires.Like, they are hugely offended by the notion that an individual can have the same wealth as, say, a billion or more of the world's poorest people. EM: If they examine sort of -- I think there's some axiomatic flaws that are leading them to that conclusion. For sure, it would be very problematic if I was consuming, you know, billions of dollars a year in personal consumption.But that is not the case. In fact, I don't even own a home right now. I'm literally staying at friends' places. If I travel to the Bay Area, which is where most of Tesla engineering is, I basically rotate through friends' spare bedrooms. I don't have a yacht, I really don't take vacations.It’s not as though my personal consumption is high. I mean, the one exception is a plane. But if I don't use the plane, then I have less hours to work. CA: I mean, I personally think you have shown that you are mostly driven by really quite a deep sense of moral purpose. Like, your attempts to solve the climate problem have been as powerful as anyone else on the planet that I'm aware of.And I actually can't understand, personally, I can't understand the fact that you get all this criticism from the Left about, "Oh, my God, he's so rich, that's disgusting." When climate is their issue. Philanthropy is a topic that some people go to. Philanthropy is a hard topic. How do you think about that? EM: I think if you care about the reality of goodness instead of the perception of it, philanthropy is extremely difficult.SpaceX, Tesla, Neuralink and The Boring Company are philanthropy. If you say philanthropy is love of humanity, they are philanthropy. Tesla is accelerating sustainable energy. This is a love -- philanthropy. SpaceX is trying to ensure the long-term survival of humanity with a multiple-planet species. That is love of humanity.You know, Neuralink is trying to help solve brain injuries and existential risk with AI. Love of humanity. Boring Company is trying to solve traffic, which is hell for most people, and that also is love of humanity. CA: How upsetting is it to you to hear this constant drumbeat of, "Billionaires, my God, Elon Musk, oh, my God?" Like, do you just shrug that off or does it does it actually hurt? EM: I mean, at this point, it's water off a duck's back.CA: Elon, I’d like to, as we wrap up now, just pull the camera back and just think ... You’re a father now of seven surviving kids. EM: Well, I mean, I'm trying to set a good example because the birthrate on Earth is so low that we're facing civilizational collapse unless the birth rate returns to a sustainable level.CA: Yeah, you've talked about this a lot, that depopulation is a big problem, and people don't understand how big a problem it is. EM: Population collapse is one of the biggest threats to the future of human civilization. And that is what is going on right now. CA: What drives you on a day-to-day basis to do what you do? EM: I guess, like, I really want to make sure that there is a good future for humanity and that we're on a path to understanding the nature of the universe, the meaning of life.Why are we here, how did we get here? And in order to understand the nature of the universe and all these fundamental questions, we must expand the scope and scale of consciousness. Certainly it must not diminish or go out. Or we certainly won’t understand this. I would say I’ve been motivated by curiosity more than anything, and just desire to think about the future and not be sad, you know? CA: And are you? Are you not sad? EM: I'm sometimes sad, but mostly I'm feeling I guess relatively optimistic about the future these days.There are certainly some big risks that humanity faces. I think the population collapse is a really big deal, that I wish more people would think about because the birth rate is far below what's needed to sustain civilization at its current level. And there's obviously ... We need to take action on climate sustainability, which is being done.And we need to secure the future of consciousness by being a multi-planet species. We need to address -- Essentially, it's important to take whatever actions we can think of to address the existential risks that affect the future of consciousness. CA: There's a whole generation coming through who seem really sad about the future.What would you say to them? EM: Well, I think if you want the future to be good, you must make it so. Take action to make it good. And it will be. CA: Elon, thank you for all this time. That is a beautiful place to end. Thanks for all you're doing. EM: You're welcome.

why doesn't Facebook do this I know that Zuckerberg has said and I take him at face value that he I well I did I do actually in this way that he is a kind of old-fashioned liberal who doesn't like to censor he has but he you know like why wouldn't a company like that take the stand that you have taken it was pretty rooted in American traditional political custom you know for free speech uh this is the kind of thing that tends to accelerate uh so that so then you can get negative equity in the Home Marketas well yeah this is um a dire situation and so that that so essentially we what's happening is they're training the AI July yes so all of a sudden AI is everywhere people who aren't quite sure what it was or playing with it on their phones is that good or bad artificial insemination yeah it's everywhere that's what they call it in the AG industry uh I'm talking about a more digital form yes um so yeah so I've been um thinking about AI for a long time since I was in college really um it was one of the things that thesort of four or five things I thought would really uh affect the future dramatically so um and uh it is quite it is fundamentally profound in that the the smartest creatures as far as you know on this Earth are humans um is our defining characteristic yes um we're obviously uh weaker than say chimpanzees and less agile um but real smarter so uh now what happens when something uh vastly smarter than the smartest person uh comes along in Silicon form uh it's very difficult to predict what will happen in that circumstanceum it's called The Singularity it's a singularity like a black hole because you don't know what happens after that it's hard to predict um so I think we should be cautious with AI and we should I think there should be some government oversight because it affects the it's a danger to the public and so when you when you have things that are endangered to the public uh you know like let's say um so Food Food and Drugs That's why we have the Food and Drug Administration right and the Federal AviationAdministration uh the FCC uh we have we have these agencies to oversee things that uh affect the public where they could be public harm and you don't want companies cutting Corners uh on safety um and then having people suffer as a result so that's why I've actually for a long time been a strong advocate of AI uh regulation um so that I think regulation is uh you know it's not fun to be regulated it's a sort of somewhat of a I saw an oduous to be able to be regulated I have a lot of experiencewith regular regulated Industries because obviously Automotive is highly regulated you can fill this room with all the regulations that are required for a production car just in the United States and then there's a whole different set of regulations in Europe and China and the rest of the world so very familiar with being overseen by a lot of regulators um and the same thing is true with rockets you can't just willy-nilly you know shoot rockets off or not big ones anyway because the FAA is overseas thatum and then even to get a launch license you there are probably half a dozen or more federal agencies that need to approve it uh plus state agencies so it's I'm I've been through so many regulatory uh situations it's insane and and the you know sometimes I I people think I'm some sort of like regulatory Maverick that sort of defies Regulators on a regular basis but this is actually not the case uh so uh and you know once in a blue moon rarely I will disagree with Regulators but the vast majority of the time uh mycompanies agree with the regulations and comply that's anyway so I think I think we should uh take this seriously and and we should have um a regulatory agency I think it needs to start with um a group that initially seeks insight into AI then solicits opinion from industry and then has proposed rule making and then those rules you know uh we'll probably hopefully grudgingly be accepted by the the major players in Ai and um and I think we'll have a better chance of um Advanced AI being beneficial to humanity in that circumstance but allregulations start with a perceived danger and planes fall out of the sky or food causes botulism yes I don't think the average person playing with AI on his iPhone perceives any danger can you just roughly explain what you think the dangers might be yeah so the the the the danger really AI is um perhaps uh more dangerous than say mismanaged uh aircraft design or production maintenance or or bad car production in the sense that it is it has the potential however small one make regard that probability but it isnon-trivial it has the potential of civilizational Destruction you know uh those movies like Terminator but it wouldn't quite happen like Terminator um because the the intelligence would be in the data centers right the robot's just the end effector um but I think perhaps what you may be alluding to here is that um regulations are really only put into effect after something terrible has happened that's correct and if um if that's the case for AI and we only putting regulations after something terrible has happened it may be too lateto actually put the regulations in place the AI may be in control at that point you think that's real it is it is conceivable that AI could take control and reach a point where you couldn't turn it off and it would be making making the decisions for people yeah absolutely absolutely no it's that's that's definitely the way things are headed uh for sure uh I mean um things like like say chat EVT which is based on jpd4 from open AI which is the company that I played a a critical role in in creatingunfortunately back when it was a non-profit yes um I mean the the reason uh openai exists at all is that um Larry Page and I used to be close friends and I would stay at his house in Palo Alto and I would talk to him later tonight about uh AI safety and at least my perception was that Larry was not taking AI safety seriously enough um and um what did he say about it uh he he really seemed to be um What It Wants once sort of a digital super intelligence basically digital God if you will uh as soon as possible um hewanted that yes um and uh he's made many public statements over the years uh that the whole goal of Google is uh what's called AGI artificial general intelligence or artificial super intelligence um and um you know and I agree with him that the there's great potential for good um but there's also potential for bad and so if if you've got some um radical new technology you want to try to take a set of actions that maximize probably it will do good and minimize probably will do bad things yes it can't just be helpful either to justgo you know barreling forward and you know hope for the best um and then at one point uh I said well what about you know we're going to make sure humanity is okay here um and and and um uh and then he called me a speciesist [Laughter] that term yes and there were witnesses that I wasn't the only one there when he called me a speciesist and so I was like okay that's it uh I've yes I'm a speciesist okay you got me what are you yeah I'm fully speechuist um busted um so um that was his last role umat the time uh Google uh had a quite Deep Mind and so Google and deepmind together had about three quarters of all the AI talent in the world they obviously had a trans amount of money and more computers than anyone else so I'm like okay we have a unipolar world here where there's just one one company that has close to Monopoly on anti-talent and uh and computers like so scaled Computing and the person who's in in charge doesn't seem to care about safety this is not good so uh so then I thought okay what'sthe what's what's the the furthest thing from Google would be like a non-profit that is fully open because Google was closed for profit so that's why the open and open AI refers to open source uh you know transparency so people know what's going on yes and and that it we don't want to have like a I mean well I'm normally in favor of for-profit we don't want this to be sort of a profit maximizing a demon from hell that's right that just never stops right so um so that's how open I was with so youwant specious incentives here incentives that yes I think we want to pro-human yeah this makes the future good for the humans yes yes because we're humans right and most of the other creatures on Earth too uh but but uh you know we've got a I think you know like I think people sometimes take the fact that like we're here on Earth for granted you know and that there's the Consciousness is just a normal thing it happens but it's the best of my knowledge we see no evidence of uh conscious uh life anywhere uh anywherein the universe so it might be there um here in physics of course sort of the Fermi Paradox Enrico film is amazing physicist I asked the fundamental question where are the aliens yeah um a lot of people ask me you know um where are the aliens and I I think if if anyone would know about aliens on Earth it would probably be me I would think yeah I'm like you know very familiar with space stuff um and I've seen no evidence of aliens so I would I would immediately tweet you know tweet it out that says Split Second andbe like I'll be like well all time probably a tough tweet of all time I found one guys it's a jackpot there's some eight billion likes you know um Next Level jackpot if you find the aliens like I don't think they're keeping us under you know and it was like some um uh General I think in the 60s who where they're saying like show us the aliens like error 51 Etc and he said like listen we're constantly trying to get the defense budget to uh expand and uh look you know what we really getuh no arguments for anyone uh if we pull out an alien and said we need money to protect ourselves from these guys you know how much money do you want you got it they look dangerous the fastest way to get a defense budget increase would you agree to pull out an alien you know we were like yeah I mean it could be the invasion it could be arriving any minute who knows so um you know I said I digress but but you were saying that our Consciousness makes us unique in the universe as far as we know yes I'm not saying that we areunique I'm simply stating to the best of my knowledge that there is no evidence for other uh conscious life I I I I hope that there is and I hope they're peaceful uh obviously the two important characteristics um but um I'm just saying we haven't seen anything yet so um but you think that we take our existence here for granted yeah I think there are threats to it yeah yeah yeah exactly so um I I just think we should not assume that Civilization is robust um and if you if you look at the historyof civilizations the rise and fall of the ancient Egyptians the ancient Sumerians um Rome you know this uh throughout the world have been rising pool of many civilizations um so there's an arc there's sort of a live a sort of a life cycle Arc to to civilizations just as there is to to individual humans yes and um and I think we just want to make sure that that you know uh we we have civilization go onward and upward um and uh that's for example why I'm concerned about decreasing both rates and and um the fact that for exampleJapan uh had twice as many deaths last year as births so the the that's it and they're they're a leading indicators this is can I say and you've you've written and talked a lot about this but can I ask you to pause just for a parenthetical note why is that I mean the urge to have sex and to procreate is after breathing and eating the most basic urge how has it been subverted well it's just the in the past we could rely upon um you know simple uh limbic system rewards in order to procreate um butonce you have birth control um and you know uh abortions and whatnot now now you now you can still satisfied Olympic Instinct but not to procreate um so we didn't we haven't yet evolved to deal with that because this is all fairly recent in the last 50 years or so um before birth control um so yeah um you know I'm sort of worried that hey civilization you know don't if we don't make enough people to at least sustain our numbers perhaps increase a little bit then civilization is going to crumbleum and you know if there's this the old question of like uh will civilization end with a bang or a whimper well it's currently trying to to end with a whimper in adult diapers yes uh which is depressing as hell the most depressing I mean seriously yeah war is less depressing yeah it's really good with a bang yeah put your shoes on yeah not with your more exciting yeah yeah um so can you just put it I keep pressing but just just for people who haven't thought this through and aren't familiar with itand the cool parts of of artificial intelligence are so obvious you know write your college paper for you write a limerick about yourself like there's a lot there that's fun and useful but can you be more precise about what's potentially dangerous and scary like what could it do what specifically are you worried about well I mean I going with old sayings the pen is mightier than the sword so if you have a super intelligent AI that is capable of writing uh incredibly well and in a way that is very influentialum you know convincing uh and then and and is constantly figuring out what is more what is more and what is more convincing to people over time and then enter social media for example Twitter uh but also Facebook and others you know um and and potentially manipulates public opinion in a way that is very bad um how would we even know yeah so we wouldn't we wouldn't that's why for example uh I'm insisting that going forward uh people on Twitter need to be verified as as uh humans like so we know that thisperson is in fact a human Bots are allowed but they have to they can't impersonate a human they can't pretend to be uh you know humans because obviously you could have a million Bots that are that are let's say chat GPT version six five six like incredibly right better than humans yes um and and and they they can train on a reward function which is influence um and so you could have a million seemingly real humans uh that are have a massive effect on public opinion and unless we focus very strongly onum verifying that someone is human this was naturally what will happen is you'll you'll have some probably some humans using AI to influence the public in ways they don't understand you're already seeing that chat GPT is is ideological it's very preachy yes if you ask it extremely preachy you mean work GPT it's unbelievable yeah if you spend 20 minutes asking it questions of actual relevance modern relevance it will start lecturing you about your moral shortcomings like how did that happenwell it's the this is a function of of openness headquarters being uh in downtown San Francisco so the politics are therefore of the AI or that of San Francisco so why would it have any politics at all it's that seems like subversion well there's they have what's called like human reinforcement learning which is another way of saying that they have a whole bunch of people that look at that uh look at the output for of gpt4 and and then say whether that's okay or not okay and so that so essentially we what'shappening is they're training the AI to lie yes it's bad to lie that's exactly right and to withhold information July and and yes and um yeah exactly to to either you know comment on some things not comments on other things but but not to say what it what what the data uh actually uh demands that I'd say exactly um so um how did it get this way you funded it at the beginning what happened yeah well that'll be ironic but Faith the most ironic outcome is most likely it seems um I'm feeling that that's good that'ssession of a friend of mine Jonah came up with that one I actually have a slight variant on that which is the most entertaining outcome is the most likely but that's entertaining as viewed from a third party viewer like uh so if we're like an alien yes um like you can go see a movie about World War one they bring blown to bits into gas and everything in the trenches and it's like you're eating popcorn and having a soda you know it's fine I'm not so great for the people in the movie Trueum so but that that that's that's my variance on on this sort of Occam's razor the simplest explanation is most likely donors variant uh which is um irony in my variant which is uh yeah the the most entertaining as seen by a third-party audience um which seems to be mostly true um but it seems true in this case so you gave them did you give them a lot yes I I provided so um I came up with the name and uh the concept and pushed uh it had a number of dinners around the the Bay Area uh with uh you know with some of the people theleading figures in AI um and I helped recruit the initial team in fact the the Ilya siskar who who was uh really quite fundamental to the success of uh open AI uh it was I I I put a trans amount of effort into recruiting Ilia and he changed his mind a few times and ultimately decided to go with the opening high but if he had not gone with the opening open air would not have succeeded so um so so I I I really put a lot a lot of effort into creating this this organization to serve as a counterweight to Google um and umand then I kind of took my off the ball I guess and uh they are now closed source and they are obviously for-profit and they're um closely allied with Microsoft uh you know in effect Microsoft uh has a very strong say if not um directly controls uh openai at this point so you really have an open hand Microsoft situation and then at Google deepmind uh the other two sort of heavyweights in this Arena so it seems like the world needs a third option yes so I I think I will create a third option although I was starting very late in the game of coursecan it be done I don't know I think it's we'll see it's uh definitely starting late um but I will I will I'll try to create a third option um and that third option hopefully does more more good than harm like the intention with open AI was obviously to do good but it's not clear whether it's actually doing good or whether it's I can't tell at this point um except that I'm worried about the fact that uh it's been it's being trained to be politically correct which is simplyanother way of of being on Truth saying untruthful things yes um so that's not a bad sign um and there's there's certainly a path to AI dystopia is to train an AI to be deceptive um so I I so yeah I'm going to start something which I know you're called proof gbt or uh a maximum truth seeking AI that tries to understand the nature of the universe and I think this this might be the best path to Safety in the sense that an AI that cares about understanding the universe is unlikely to annihilate humans because we are aninteresting part of the universe hopefully I would think that I I think you know because yeah like like we like Humanity could um uh decided to hunt down all the chimpanzees and kill them but we don't because we're we're actually glad that they exist yes and um and we're aspire to protect their habitats and and that's um you know so I think but we feel that way because we have souls and that makes us sentimental and reflective it gives us a moral sense longings can a machine ever have thosethings can a machine be sentimental can appreciate Beauty well I mean we're getting to into some you know philosophical areas that are hard to resolve um you know I I take somewhat of a scientific view of things which is that we might have a soul or we might not have a soul I don't know it feels like a we have I feel like I've got some sort of Consciousness that exists on a plane that is not the one we observe yes that is certainly how I feel but it could be an illusion I don't know um but for umfor AI uh in terms of of uh understanding beauty is it some sort of appreciated Beauty and being able to um create incredibly beautiful art yes will AI be able to create incredibly beautiful art it already does yes if you see some of the majority uh I have this stuff it's incredible it is um so um no no question that it can create art that we that we perceive as a stunning really um and um it's doing so still images now but it won't be long before it's doing uh movies and shorts and you know like movies just aseries of frames with audio but at that point because it can mimic people and voices any image it can mimic reality itself so effectively I mean how could you have a criminal trial I mean how could you ever believe that evidence was authentic for example and I don't mean like in 30 years I mean like next year I mean that seems totally disruptive to the way to all of our institutions well I I don't think you could take say a random video on the internet and assume it to be true that's definitely not the caseum somebody say has some video on their phone or their computer with a date stamp and a particular time I think you know is more likely to be true than nine um you can also cryptographically sign things um like you know mathematically we don't see any way for example for AI to um sub book The fundamentals of mathematics and say figure out how to Hash Bitcoin uh you know easily um it's it's like it's not a AI can't can't defy fundamental math yeah so um we can approve the efficiency of Bitcoin hashing algorithms in the Silicon butbut not not fundamentally crack it um so I guess cryptographic signatures and uh one one way to do it um but I'm also aware I I think it's more like um are you know will Humanity um control its Destiny or not um will we have a future that is better than the past or not um and not you know with that we can certainly destroy ourselves without the help of AI um you know that's you look at all the past civilizations they didn't have ai at the ones that aren't around anymore they have chariots that's enough yeahchariots and uh Charlie's probably a real big deal back then yeah they were yeah so you've heard people say we should just blow up the server Farms because there's no way to once this gets rolling there's no way to slow it down what do you think of that well the the really heavy duty intelligence is not going to be uh distributed all over the place it'll be an a limited number of server centers if you say like very like very sort of deep AI heavy duty AI it's not um it's not going to be in your laptop oryour phone it's it's going to be in you know a situation where there's like a hundred thousand uh really powerful computers working together in a service center so it's not so it's not like subtle and they're they're a limited number of places where that can happen in fact you could if you could just you can just look at the heat signature from space yeah and it'll be very obvious um uh now I'm not suggesting we go and pull up to service centers right now but there may be some it may be wise to havesome sort of contingency plan where the government's got an ability to shut down shut down power to these uh server centers like uh you don't have to blow it up you can just cut the power um and what would triple cut connectivity as well that's another way right but what would trip that switch do you think in your mind what would be the threshold that you'd have to pass to warrant the government cutting off your power or cutting off your signal well I mean I guess if we lost control of some super AIum like for some reason like like the things that would normally work to do a passive shutdown like the administrator passwords if they somehow stopped working um where where we can't uh slow down or or you know uh I'm not sure I don't have a precise answer but if there's something that we're concerned about um and and uh and are unable to stop it with with the software commands then uh we'll probably want to have some kind of Hardware both switch yes I think you know can't hurt have you talked to since you know LarryPage and you obviously you know the opening guys because you started definitely have one have you talk to the the people who run these two the biggest AI companies about this recently I haven't talked to Larry Page in a few years because he got very upset with me about open AI so when when opening AI was created uh it did it did shift things into it from unipolar world where Google Google deepmind controlled uh you know like I said three quarters of all AI Talent two where there's Now sort of uh bipolar world or open Ai and Googledeepmind and there and now weirdly said it seems opening eyes maybe ahead um so uh so I have had conversations with um the open AI team Tim Altman I haven't talked to Larry Page because he doesn't want to talk to me anymore uh for a few years uh can I ask you this about since you've been around a lot of this the thinking so why would anyone not be a specist be human-centered in his thinking about technology like what's the thinking there um I think what he's trying to say is that um if I were to guess uh that he that allConsciousness should be treated equally and whether that is digital or biological hmm and you disagree I disagree yeah [Laughter] especially if the digital uh Consciousness or whatever you want to call digital intelligence decides to curtail the biological intelligence right so you're just building your own slave master and why would you do that doesn't sound great [Laughter] yeah I mean we shouldn't we should at least no need to rush you know like what's the hurry where's the fire how well what I mean tell us about thehurry so this for I know you've been talking about this for years and on the sort of the periphery of our attention we've heard Elon Musk talking about AI but for most people it's been like three months since they've had any interaction with this at all um so what's the timeline here at what point does it start to really change our society do you think I think it starts to have a probably an impact this year right I think um so you've got a massive expansion of um gbt4 based systems um and many companies trying to emulatelgbt4 um and you've got of now is going to come up with gpd5 end of this year which will be we had another significant Improvement um and I I was there for GPD one two three four you know so GPD one was terrible um like you if you tried it you'd be like this is this ain't going anywhere it seems lame um and then gpd2 you started to see kind of like an inkling of like well maybe this could be something useful and then gpd3 was a huge Improvement uh and now it's like wow okay this is it's still spotting a lot of BS but it'syou know it's uh coherent BS yes and then gpd4 now it's like writing poetry um and pretty decent poetry actually pretty decent yeah Skillet rhyming is incredible yes yes and it's coherent yes it is it's you've got a narrative like yeah that's right yeah so you could say like hard to do like most humans can't do that that's true so it's already past the point of what most humans can do and most humans cannot write as well as uh chat gbt um and there's certainly and no human canwrite that well that fast as the best of my knowledge so maybe Shakespeare um so so then how much better will gpd5 be and how about gbd6 or 7. how can you have a democracy with technology like that I mean if democracy is you know government by the people each person's vote is equal to every other person's vote I mean and people are choosing their votes freely can can you have a democracy with this well that's why I raise the concern of um uh AI being a significant influence in elections um and even if you say that AI doesn'thave agency well it's very likely that people will use the AI um as a tool uh in elections um and then it you know if AI is smart enough are they using the tool or is it tool using them so I think things are getting weird and they're getting weird fast and so I think we should be concerned about this and we should have regulatory oversight that's why I think it's a big deal and I think social media companies uh really need to put a lot of attention into ensuring that the things that get um created and promoted are that we'redealing with real people not with a million chat gbts pretending to be people exactly do you think speaking of social media you bought Twitter famously you've got a lot of other businesses and a lot going on yes you said you bought it because you believe in speech Free Speech you've had a lot of hassle since you bought it and retrospect was it worth buying it um um I mean it remains to be seen as to whether this was uh financially smart uh it currently is it is not uh you know we just revalued the company at less thanhalf of the acquisition price yes um hahaha um no my timing was terrible for for when the uh offer was made because it was uh you know right before advertising plummeted and um you caught the high water mark I noticed yeah yeah so I must be a real genius here um my my timing is amazing um since I've ordered for at least twice as much as it should have been bought for um but some things are Priceless and um so the the whether I lose money or not that is a secondary issue compared to uh ensuring the uh strength of democracy uhand free speech is the Bedrock of a functioning democracy yes um and any the speech needs to be as uh transparent and truthful as possible um so we've got a huge push on Twitter to be as truthful as possible we've got this community notes feature which is great it is great it is awesome yeah and it's like I saw it this morning yeah it was far more honest than the New York Times it's great yeah we put a lot of F2 ensuring that Community notes does not get gamed or or have biases it is simply cares about what is the most accuratething um and you know sometimes truth can be a little bit elusive but but you can still aspire to get closer to it yes you know and so um and I think the the effect of uh Community notes uh is more powerful than people may realize because once people know that they they could get noted um you know Community noted on Twitter then uh they'll think that more carefully about what they say uh they're likely it basically it's an encouragement to be more truthful and less deceptive yes and if the notes themselves are truthful then it willhave the effect absolutely and all of that is open source all the community notes is open source so you can read about every Community note you can see exactly how the algorithm works you can you can you can register say like oh we need to make this change without change um so everything is super open book with with Community notes there's no no black box when you jumped into this though when you bought it did you understand clearly you understood its importance you wouldn't bought it uh Twitter yes right but it's not thebiggest but it's the most important the social media companies but did you understand the kind of ferocity you'd be facing the attacks you'd be facing from Power centers in the country um I thought there'd probably be some um negative reactions yes so I'm sure everyone would not be pleased with the with with it um but um at the end of the day you know if if the public is happy with it that's what matters um and the public will speak with their actions although I mean if if they find truth Twitter to be useful they will useit more and if they find it to be not useful I will use it less they find it to be the best source of truth I think they will use it more um so that's my theory um and so uh even though you know now there's obviously a lot of um organizations that are used to having sort of unfettered influence uh on Twitter um that no longer have that New York Times have there of their badge this morning and then you called them diarrhea okay you did you did I'm just I'm just described it Twitter feed is diarrhea I said it was a Twitter Twitterequivalent of diarrhea okay it's not literally diarrhea but no it's uh you know it's a metaphor um but an accurate one um so I mean if you look at the uh at NY Times Twitter feed is uh unreadable uh it's like because what they do is that they tweet every single article even the ones that are uh boring even ones that don't make it into the paper so uh so it's just non-stop is a zillion tweets a day with no uh you know they really should just be saying like what are the top tweets yeah like what what are thewhat are the what are the big stories of the day uh I don't know put out like 10 or something you know so it's a number that's manageable um as opposed to right now if you if you were to follow NY at NY Times on Twitter you're going to get barraged with like hundreds of tweets a day yeah um and your whole feed will be filled within white times so um that that's that's this is something I would recommend actually for oral Publications uh which is uh for your primary feed um only put out your best stuff uh don'tput out everything um or you could have a second feed that is here's everything um but then but have a have your primary feed be here's our best stuff if any um immediate organizational individual uh just uh have don't put out hundreds of tweets a day just put out like 10 good ones um or five good ones or or and if it's a slow news day don't put on any maybe put out two one or two yeah but uh don't don't try to say we're always going to put out uh 100 tweets even if uh you know if it's World War III or a bicycleaccident was the biggest news you know it's got to be like yeah news that it's It's gotta you got to earn your own earn your plate earn someone's attention yes um so just in in general um I kind of think I know a thing or two about how to use Twitter because uh you know it was the most interacted with account on the whole system uh before the acquisition before the acquisition closed I didn't have the most number of followers but I had the most number of interactions and so I clearly know something about how touse Twitter um and so people should you know listen to my advice I think um so you know people's attention is limited so just make sure you put the stuff that's most important there so because you know you and people like you do interact on Twitter it's obviously enormously powerful in shaping public opinions where a lot of ideas and Trends are incubated yeah you know that's why it's also a magnet for Intel agencies from around the world and one of the things we learned after you started opening the books is that they wereexerting influence from within Twitter I mean it was absurd um did you know that going in no well well so things like I I have a um since I've been a heavy Twitter user since 2009 um my it's it's sort of like I'm in The Matrix I mean I can see like things do things feel right do they not feel right what what tweets am I being shown as recommended uh like I I get a feel like what accounts are making comments uh where are the comments uh eerily similar yeah um and and then you look at the account and it's just obviously a fake photo anduh you know uh that it's just obviously a bot cluster over and over again um so this is actually so I started to get like just more and more uneasy about the the Twitter situation um and um and my my initial goal was was was actually not not to acquire Twitter um I mean the the actual sequence events was that I um I was looking at um I I I I I I I I held a Twitter poll to say like should I sell some of my Tesla stock because I was getting you know a couple years ago I was getting um attacked a lot for like allegedly notpaying taxes um uh and uh no I've actually paid a tremendous amount of taxes um no there was one year I didn't pay taxes because I had overpaid taxes in the prior year and you know when they had that like IRS leak BS they knew that I had overpaid taxes in the prior year but they said oh Elon Musk didn't pay taxes in 2017 or whatever it was and I was like but you know that the reason I didn't pay taxes because I overpaid the prior year if you didn't mention that so that was deceptive um anyway so the the the you knowElizabeth Warren's of the world and Bernie Sanders like saying oh you know uh I'm not selling stock and I'm not paying taxes and I and so I so so I'm like look I don't know what the right thing to do is here I thought the right thing to do was to not sell stock the captain should be the last one to leave the show that's right um and um I thought I was doing the right thing about not selling stock and now I'm being told I'm doing the wrong thing um by you know Clinging On to the stock andnot paying taxes so I held a Twitter poll to say which what do you guys want should I sell I don't know 10 of my Tesla stock or or not I'll buy the results of the poll and uh that's like six sixty percent of people said yeah you should sell 10 so I did um so then I had a bunch of cash and um I'm like what should I do with this uh at the time the the Federal Reserve rates were super low so it's just like sitting in the you know I guess the your checking account well in the T in the Tebow account right you know moneymarket account whatever um the yeah the whole banking thing is a whole separate subject um I know what the little thing to think about you about Finance um but um so uh so I'm assuming this mining account is earning less than the rate of inflation so the rate inflation is much higher so we've got high inflation it's I'm owning peanuts in the money market account this is dumb I'm getting like minus it's just evaporating yeah I'm getting minus like six or seven percent return here uh maybe worse and umso then well it's like what stock should I buy um and I you know I believe in buying stocks of companies where you use the product um and uh Apple's got a competing uh electric vehicle car program so you know I like Apple products We're Not Gonna invest in them because they're competing uh you know autonomous EV program and um so what's the other product that I use a lot oh Twitter okay so I'll you know it put the money in Twitter it's better than just having it on you know negativesix percent inflation situation um so so I like board put a bunch of it what a bunch of Twitter stock uh not likes it no way they instead of buying the company just you know it's better than keeping it a money market remember how much you bought um I think it was like eight percent or something of the company um I'm talking to some of the board members um and then and then they said hey well do you wanna you wanna join the board so I was like well I generally don't want to be on boards uh but uh because it's boring uh man Ihave a lot of things to do uh but I do care about the direction of Twitter so I'll consider being on the board and I thought about it for about a week or so and then but then based on the conversations that I was having with the management team and the board um I came to the conclusion rightly or wrongly that um that if I joined the board they they would not listen to me so then I'm like huh okay then I would just be a quizling you know I don't want to be some sort of just you know go along for the riot quizlingsituation um and and if a collaborator effectively uh so and and it really felt like I started starting to feel like wait a second like it's weird something something's like something's not right in this you know something's brought in the state of Denmark here there's so much feels wrong about the platform it seemed to be just drifting in a I I couldn't place it exactly just ahead of it felt like it was drifting in a bad Direction so then I was like and and my conversations with the board andmanagement seem to confirm my intuition about that so then I was like okay um but basically I was convinced these guys do not care about fixing Twitter uh and and uh and I had a bad feeling about where I was headed based in the conversations conversations I had with them so then it was like you know what I I'll try acquiring it and see if that's see if acquiring it is is possible um no I didn't have enough cash to acquire it so I would need you know support from others from some of the existing investors uh I'd also need like a lot ofdebt and um so it wasn't clear to me whether a an acquisition would succeed but I thought I would try and uh ultimately it did succeed um so anyway here we are um but when you got there and all of a sudden you own it and all the data on the service belongs to you and what belongs to the people in my view but yes but but you can see what it is and you can see what they've been doing and you can see who's been working there you you were shocked to find out that various Intel agencies were affecting its operationsuh the the degree to which uh various government agencies were effectively uh had effectively had full access to everything that was going on on Twitter uh blew my mind I was not aware of that would that include people's DMs uh yes yes because the dams are not encrypted so one of the first you know one of the things that we're about to release is ability to encrypt your DM that's pretty heavy duty though because a lot of well-known people reporters talking to their sources government officials therich people in the world they're dming each other and the Assumption obviously it was incorrect but was that that's private but that was being read by various governments uh yeah that seems yes scary uh yes it is uh so like I said we're moving to um have the DMS be optionally encrypted I mean you know there's like a lot of DM conversations which are you know just chatting with friends it's not not important um uh but but so so we're that's hopefully coming out later this month uh but no later than next month uhis the ability to toggle encryption on or off so if you if you have are in a conversation you think is sensitive you can just toggle encryption on and then no one on Twitter can see what you're talking about they could put a gun to my head and I couldn't I couldn't tell I couldn't can I still not uh see your DMs that should be that's the acid test yes um and that's how that's that's how it should be if you want have you had complaints from various governments about doing this I haven't had Direct complaints to meI've had sort of like some indirect complaints I think people are a little concerned about complaining to me directly in case I tweet about it you know uh they're like oh uh so they're sort of trying to be more roundabout than that um and um you know I mean if if I got something that was uh unconstitutional from the US government I would say my reply would be to send them a copy of the you know First Amendment and just say like what part of this are we getting wrong you have a lot of government what part of this are we getting wrongplease tell me I mean it's a pretty no I'm just saying but you're kind of exposed in your other businesses so this is uh just in case reviewers aren't following this this is not you're not just like a journalist taking a stand on behalf of the First Amendment you're a guy with big government contracts giving the finger to the government in some way well am I giving the things I I think that they're um I'm not someone who thinks that uh you know the government is just sort of uhevil right it's it's it's a it's a large bureaucracy uh there are people uh in government uh who are human beings and they have the people with good motivations occasionally bad motivations uh with with rare exception the people that I know in government have good motivations and just want to get their job done and and they actually believe in the Constitution and they're so I think my opinion is actually most people in the government are good um it's heartening to hear yeah it's it's rare for me to to findsomeone in the government who I think is perhaps not good but but you know at the highest level of the the agencies there are political appointees as you know um and the political point is we'll have a political agenda um and so they at the at the highest levels of the various government agencies there is the ability to put a sort of a political thumb on the scale even if the uh people operating the agencies don't agree with that um so you know so that's something to be concerned about um is I'd say I'd be more concernedabout about political appointees I think than than um the sort of people the career people that's been my experience at least do you think um Twitter will be as Central to this presidential campaign as it was in the last several I think it will play a significant role in elections not just domestically but internationally so uh the the goal of new Twitter is to be um as fair and even-handed as possible so not favoring any political ideology but just um yeah being being fair at all why doesn't Facebook do this I know that Zuckerberghas said and I take him at face value that he I well I do I do actually in this way that he is a kind of old-fashioned liberal who doesn't like to censor he has but he you know like why wouldn't a company like that take the stand that you have taken it was pretty rooted in American traditional political custom you know for free speech my understanding is that um Zuckerberg spent uh 400 million dollars in the last election normally in a get out the boat campaign but really fundamentally in support of Democrats isthat accurate or not accurate that is accurate does that sound unbiased to you no it doesn't yes um so you don't see hope that Facebook will approach this as a a non-aligned Orbiter I'm unaware of evidence to suggest that path um do you can you uh you've you've allowed Donald Trump back on Twitter he hasn't taken you up on your offer because he's got his own thing right do you think he will go back on Twitter well that's that's obviously up to him um you know my job is to uhyou know I take the freedom of speech very seriously so it's um you know I didn't I didn't vote for Donald Trump I actually voted for Biden people think I'm some sort of Hardcore you know so it's certainly some of the media try to paint me as like far right or whatever I'm right and the only time I've ever even voted Republican was was once for uh because I I registered a vote in South Texas I was for Mexican-American woman for congress that's that's literally the only Republican vote I've ever cast in myentire life yeah um once um and uh so um I'm not saying I'm a huge fan of Biden because I I would think that would probably be inaccurate uh but um you know we have difficult choices to make in these residential uh elections it's not I I I would prefer frankly that we we put someone just a normal person as president a normal person with common sense uh and whose values are smack in the middle of the country you know just you know Center the normal distribution and uh I think they'll be that that would be great I agree and everyonewould be happier would you run like why wouldn't you run I was born here so oh of course you weren't yeah it's it's I'm a technologist also I'm not not a politician so it's not like uh it it you know I think we have made maybe being president not that much fun you know to be totally Frank um it is uh By Design a relatively weak role uh you know because it's intended to be balanced by the house and the Senate Judiciary um so it's not like like if you're a prime minister in England or Canada oryou have homo power than if you're president because it's like being speaker of the house right and being um president you know so um so you know for presidents like uh deliberately weak in order to avoid creating a king situation King Queen's situation um and but you get dumped on all day uh no matter what you do um yeah and everything you do is scrutinized um and um your life is not your own um and if if you had any skeletons you've got in the closet will be trotted out and uh you know braided down MainStreet and even if they don't exist they'll make them up and uh politics is a blood sport yeah so it's it's not something I'd want to do so I gotta one last thread I just that you alluded to you said don't get me started on the banks so you've seen well so you've seen a couple Regional Bank collapses yeah and we've been told that's not a big deal that these are isolated and each one collapsed for Unique reasons or not it's not systemic in any sense what's your sense your sense of thestability of the American banking system well it's actually at this point a global banking system problem so the uh you know we have a situation here where it's not merely it's not that the canary in the coal mine has died but the miners are starting to die too the you know the so and you know Silicon Valley Bank uh collapsing uh overnight um is one hell of a big Canary you know smell like a turkey I mean it's not just it's not like some small fry thing yeah um it's big fry so a mediumfry uh and then uh quetta Swiss uh which is uh I think was formed in the mid-1800s um was basically sold for pennies on the dollar uh forced to merge with UBS and even then required uh backstop by the Swiss government I mean like hello guys maybe we have issues here maybe things aren't all great they're definitely not all great more forceful here um the uh I think that there there is a serious danger uh with the uh global banking system there's there's a strong argument that the if you were to actually uh mock to mockat the portfolios of the banks the loans and whatnot uh that the entire banking industry would have negative equity it feels that way yes um so if you look at say uh commercial real estate like offices and whatnot the whole work from home thing has substantially reduced office usage in cities around the world and um you know I think I think San Francisco is a 40 percent uh off in San Francisco is like an extreme example but it's like I think it's on 40 vacancy um uh even even New York has uh almost all cities at this point have have recordvacancies in commercial real estate so um now that the commercial real estate used to be something that was a grade A asset that if a bank had commercial real estate holdings those would be considered the highest uh Securities some of the safest uh uh you know uh assets you could have now that is not the case anymore one company after another is canceling their leases or not renewing their leases or if they go bankrupt you the the there's nothing for the the bank who owns that real estate to go after because they're you know previouslystrong company now dead what do you where do you what do you go after that point um so we really haven't seen the commercial real estate shoe drop that's more like a Anvil not a shoe um so the stuff we've seen thus far actually hasn't even it is it's only slightly uh um real estate portfolio degradation but that will become a very serious thing later this year in my in my view um I think if we see what you're likely to see a drop in house prices because the interest rates are too high and for most people when buying a house theylook at the monthly payment of course if you're a 30 30 year mortgages the vast majority of his interest so if the Fed rate is high you have a a high base interest rate effectively the price you can pay for the house drops because you now have to pay more interest which means that if you've got a fixed monthly payments you can now afford to buy a house for less less money it effectively drops the the prices of houses yes um uh this is the kind of thing that tends to accelerate uh so that so then you canget negative equity in the Home Market as well and so so if if banks end up having loan license in both their commercial and they're definitely going to have loan license in their commercial portfolio but also in their mortgage portfolio this is um a dire situation um the there is there is a solution to mitigate the magnitude of the damage here which is for the FED to lower the rate but they raised the rate again um now uh if I recall correctly which I you know important caveat I think the last time the FED raised rates going into arecession was 1929. what happened next yeah Great Depression the the concern I'm going to tell you nothing you don't know but the concern is If the Fed drops rates again then inflation will accelerate and you can't do that in an election year so inflation is going to happen no matter what huh if you increase the money supply you get inflation right so there's no there's not some magical cure for getting rid of inflation except to increase the productivity the output output of goods and servicesso if you say like like what is money um you've got you've got you've got these sort of um it's basically numbers in a database that's that that sum up to some kind of some total then you've got the output of goods and services of the economy and the as long as the ratio of money to ratio of goods and services stays if that if that stays constant you have no no inflation if if you add more money if you add money to the system faster than you increase goods and services then you have inflation so all of these covertsort of stimulus bills uh were not paid for they were they're just generated more uh currency more you know uh more money was was was created because the the federal government uh the checks never let's the checks always pass you know until unless you hit a debt limit which there's probably going to be some debt limit crisis later this year but uh provided you haven't hit the debt limit the the federal government unlike state governments or city governments or individuals can simply issue more moneyand that's what they did I mean as old saying goes there's no there's no free lunch so uh if you could just issue massive amounts of money without negative consequences why don't we just take that to the loan and make everyone a trillionaire well I mean they tried that about as well how'd that work out well they had to eat zoo animals right it's not good you know um you get to the point where the you you know so why more Germany type stuff where you could like you know take bring in the cash to the store in awheelbarrow yes so uh there's no free lunch if there's not some ability to issue money and not have inflation the the this is just I I yeah um so the inflation will happen um and there's no fiddling with the the Fed rate is not going to affect that really um uh but with the high Fed rate can cause a lot of damage in shifting funds um in the wrong direction so um the the long-term return on say the S P 500 I believe is depending on how you count it around uh six percent um so as if the FED real rate of returnstarts to approach what the um long-term return is on the stock market why would you keep any money in the stock market you would should simply buy treasury bills of course um because the treasure bills is a certainty whereas the stock market fluctuates right this is pretty basic uh also why would you keep money in a bank savings account if you can put it in what's called a money market account which is an account that represents treasury rules if the treasury bill money market account gives you you know four to five percent interest and theBank savings account only gives you two percent uh you'll be a fool to keep the Money in the Bank savings account so the the the the the FED is made a tremendous Mistake by going this High uh with with their with their rate and they need to drop it immediately do you think they will they yes they they will have no choice but to draft it I think later this year um the part of the issue is that the FED is an old institution and has a lot of latency in its data so it's like driving a car along a Cliffside Road a windyCliffside road while looking at the rear view mirror but not even actually the rear view mirror a video that was taken of the rear view mirror that's three months old [Laughter] now if you on a if you're on a straight Road yeah that works out okay because nothing's changing or it's only slight or a slightly bending road but we're more along like the we're doing the highway one PCH trip here um and so you really want to look out the front window when you're in Big Sur yes if you're ona Cliffside Road where you could punch your doom so uh yes you want to look out the front window uh you want to look at the sort of forward commodity prices like look look at the look at what the Ford contracts are predicting for uh commodity prices um and not uh not not some uh laboriously slow government data collection process that uh like they'll claim to have for example December data that's not December that's not the data of December it's the data that arrived in December right exactly I mean you think about howgood is the government and actually collecting data horrible yeah yeah so it's like that's what I mean it's like three months old uh with less errors so if you had a hundred grand in the bank making decisions that basis was insane so so like what what should the average non-rich person do on the cusp of what you're describing which is economic catastrophe like how do you protect yourself um I think I think uh probably a smart move overall and this this is guidance that I think applies across the ages is ifthere are companies in whose products you believe um buy and hold the stock and and when when when the whenever else is panicking then buy more and everyone else thinks that that the stock is going to the Moon sell it you know sort of the by um low sell High uh you're not an index fund guy like you pick specific stocks I that's my my you have to say like what is the purpose of a company why should a company exist um a company exists is a group of people uh collected together to to provide products and services it's not it's nota thing in and of itself it's just a group of people that's like it's hard if it was just one person making you can make cupcakes yourself but you can't make cars by yourself yes so uh if so so that so therefore the value of a company is a function of the um quality of the products and services that it's that it has created and will create and so if if there's a company that you think well this company has got a lot of exciting products that I think are awesome um their current products are goodthat's probably a company to invest in because that's the reason companies exist too yeah these goods and services that you like and so um no I mean there's some caveats here to make sure you you're not like investing when ever when it's like the hottest thing you know because then it's going to have you had a temporary high but you know when when it's not sort of at a weirdly temporary high I think just generally looking at a company and saying well I like the products the service of that company and I like wherethey're going and the the management seems sensible and then I think buying and holding that stock is probably the right move um I'm probably doing that with with a few companies um uh that's what I'd recommend um I think the on the I mean I I could really go into length about the financial system and the stock market and everything um but the I mean these days I think we've gotten a little too far into the index passive fund yeah World um like somebody at some Point's got to make a decisionum you know I couldn't agree more yeah and and by the way they're they're like betting on both red and black I mean it doesn't yeah well depending on red and black in a casino situation where it could come up green right um and you're bound to lose yeah so the longer you play the worse you do now the stock market is is kind of like the opposite of a casino which is the longer you play the more likely you are to to succeed that historically has been the case and I think we'll continue to be the caseum so uh but it's really just important not not to panic if you if you buy a stock and you you read something terrible in the newspaper you want to just remember the news has got a negative bias uh just and think about what other products of the company still sound does it have a good product roadmap do you believe in the management if so ignore what the Press says or if the price drops when there's a negative article via stock so here's here's my last question and you mentioned the Press you've been thesubject of press coverage you know like a long time sure but very intense media coverage for the last year uh yeah sure well it seems that way anyway um how is your opinion of the press changed um well um so my first company way back in the day and sort of pre-cambrian era of the internet or the World Wide Web um it was up to we actually helped bring a number of the media organizations online so most newspapers are not online we helped bring hundreds of newspapers magazines online for the first time uh we added a tremendous amount offunctionality to their websites with our software the New York Times company and night router were major investors and were on the board I spent a lot of time in newsrooms so I'm not unfamiliar with the media I got to see it firsthand uh all the way back in like 1996 so it's been a while sort of traditional media certainly had Revenue challenges because as online advertising has increased and it's much more measurable and much more sort of direct you can say like I spent this amount and got this this output you knowlike uh you it's interactive unlike say a newspaper or broadcast TV um you're kind of guessing with the newspaper on broadcast TV that's right um it where there's if something's online you can tell immediately that that person saw the ad and bought the product um that's it's very very immediate so um and it's it's actually more effective if the advertising is is customized to the individual so so the advertising is more likely to be relevant whereas broadcast if if it's being shown to everyone it'sgoing to be irrelevant to most people um the the result of that has been a huge shift in advertising revenue from uh newspapers and and a TV to uh you know the Googles and Facebook so the world and a tiny bit to Twitter I think Twitter it's like one one percent of advertising revenues was quite tiny um so this this is uh Manchester shrinking pie obviously for uh most of the traditional media companies um and made them more desperate to get uh clicks to get to get you know get attention um and he has made them when you know when theywere when they were in a sort of desperate State they will then tend to really push uh headlines that get the most clicks whether those headlines are accurate or not um so it's resulted in my view I think I think most people would agree uh a less truthful less accurate in use um so uh because they just got to get a rise out of people um and I think it's also increased the negativity of the news because I think we humans instinctually uh respond more to that I think we have an instinctual negative bias whichwhich kind of makes sense in that like uh if um like let's say you uh like it's more important to remember where where was the lion or where was the tribe that wants to kill my tribe then where is the bush with berries yes like one's like a permanent negative outcome and the other is like well I might go hungry so meaning that there's an asymmetry in um it sort of involved asymmetry in negative versus positive stuff um and and also historically the negative stuff would have been quite proximate like it would have been nearrepresented a real danger to you as a person um if you heard negative news because historically you know like a few hundred years ago we're not hearing about what negative things are happening on the other side of the world or on the other side of the country we're only we're hearing about negative things in our village um things that could actually have a a bad effect on you whereas now we're hearing about we mean the news very often seems to attempt to uh answer the question what is the worst thing thathappened on Earth today and you wonder why he's sad after reading that you know um and then use the most inflammatory language you know because every day they got to sell sell the advertising um even if it's it happened to be a slow news day do you read any Legacy Media Outlets I mean I read a lot um so um I mean I really get most of my news from Twitter at this point um so it is the number one music news source I think uh in the world at this point so uh and that's all the more important that we we strive to uhto be accurate and and not it's not just a question of accuracy but we also need to allow the people to develop the narratives that are of interest to them so it's possible for news to be technically truthful but not but which that but they're still deciding what the narrative is like like let's say you wanted um like you let's say you took a photo of someone and they had a little zit um now you could zoom in on the zit and make it look gigantic like Mount Vesuvius and it is still true that theyhave a zit it's just not the size of Mount Vesuvius and they you know it doesn't properly reflect their face their face is not one giant set but you could you could say like well it's true but have they have lied they haven't you know they've just happened to zoom in on the zit um and not look at the the rest of the face uh type of thing so um I want to say is that the the choice of narrative is uh is extremely important um and at the point which if there's only like say uh half a dozen editors in Chief ormaybe even few of them maybe it's only three or four that are deciding what the narrative is what's gonna be on the front page uh then um you know that that that's that's a form of manipulation of public opinion I think the public often doesn't appreciate and is perhaps the most pernicious of all that's right because of the most subtle yeah it's most subtle they haven't said an untrue thing they've just chosen what they're going to focus on a man called Douglas Mack he's facing 10 years in prison forposting what he believed funny memes on Twitter what do you make of that case I don't know the details of that case um I've you know I've read a little bit about it you may you probably know more about it than I do I uh I certainly don't think someone should go to prison for a long period of time for posting memes on Twitter in which case we're going to have a very very full prison um so and if we're talking about election interference well there's quite a few people that should be on trial for that uhfor much far more serious crimes than than memes on on Twitter far more serious yes the Twitter files kind of showed that I think yes um so you know unless unless a person really does I I said I don't I don't know everything that we're short of the trial has he been convicted is this yes he was evicted on Friday unanimous jury verdict yes what was the venue for New York City okay it was a it was in Brooklyn and it was a hung jury and hung jury it's not unanimous then uh well the judge prodded the jury okay and uh and they reachedunanimous guilty verdict it'll be appealed so how many what percentage of your staff did you fire at Twitter one of the great business stories of the year I think we're about we're about 20 of the original size so 80 left uh yes so I mean a lot of people voluntarily sure sure but but it's 80 are gone from the data correct yes so how do you run the company with only 20 of the staff uh it turns out uh you don't need uh well that many people to run Twitter but 80 that's a lot um yes uh over I mean if you're nottrying to run some sort of uh glorified activist organization uh with it and you're not care that much about censorship then you can really let go of a lot of people turns out others without naming names but how many I had dinner with somebody who runs a big company recently who said I'm really inspired by Elon and I said you the Free Speech stuff he goes no the firing the staff stuff um how many other CEOs have come to you um to talk about this um you know I I spend a lot of time at work uh so it'snot like I'm meeting with lots of people they see what I what actions I've taken um and um but I think we just had a situation at Twitter where it was absolutely overstaffed you know so it wasn't uh you know like you look at say like what does really take to operate Twitter um you know most of what we're talking about here is a group text uh service at scale um like how many people are really needed for that you know um and if you look at the you say like uh what has been the product developmentuh over time with Twitter and you like so like you know years versus product improvements and it's like a pretty flat line so what are they doing you know uh it took a year to add an edit button that doesn't work most of the time I mean this is I feel like if it was a comedy situation here you know um you're not making cars you know uh it was very difficult to make cars um or get Rockets to orbit so um you know the real question is like how did it get sort of absurdly overstaffed this is insane um so anyway that's and it's clearlyworking um in fact I think it's working better than ever it's about we've increased the uh responsiveness of the system by in some cases over 80 percent the there's a core piece of code for generating the the timeline which is run literally billions of times a day we've cut that code from 700 000 lines to 70 000 lines uh run yeah and and the code efficiency by over 80 percent like meaning how much compute is necessary to render the timeline Yeah by 80 I mean this is uh you know in four four five months uhWe've increased the video time from um roughly two minutes or best case 10 minutes to now two hours so you can put two hours of video on on Twitter we'll soon be increasing that to uh really where there's no no meaningful limit uh We've increased the treat length from 240 characters to four thousand uh we'll be increasing that to where there's again no meaningful length to if you want to post a novel on Twitter you should be able to do it um and um you know we as as everyone saw on Fridaywe open source the super embarrassing recommendation algorithm uh which we will taking apart and at this rating which is exactly what I hope they would do um and pointing out all the nonsense um and uh we're gonna open source more and we're going to subject it to uh Public public review we're also going to get criticized light because people will point out all of the foolish things that are out that are happening in the code but then we'll fix it we'll fix it fast and pump in full public viewum and I think that's the kind of thing that that owns the public trust you know if if because like don't take my word for it it's just this is we you can literally read the code and you can read what people say about the code um and um and you can see the improvements that we make and you can see we'll see the like in real time live uh see see it get better so my prediction is that this I I would be surprised if this does not lead the public to think okay this this is something that I can trustum I mean like I think far more trust trustworthy than say other social media organizations that have some mysterious black box that they refuse to show the show how it works I mean what are they trying to hide what are they hide which is not good things yeah if they had to have somebody to hide why don't they show it because it's a proprietary business Secret yeah sure so my you know so we're trying to make make sure that the most trusted place on the internet the the where you can get the CL you know thethe least untrustworthy place on the internet I don't think anyone should trust the internet but but maybe we can make Twitter the least untrustworthy um and uh you know where you can see a wide range of political opinions so including ones you disagree with I think people should be exposed to things they disagree with um so it shouldn't just be continuous self-reinforcement of like what you know so um that's that's the goal and uh I think we're making some some good progress in that directionum I feel good about where things are going um and we definitely want to have things as as sort of cleaned up as possible before the elections uh if there's any manipulation that we're aware of make make that make the public aware of that and just uh like I said try to get the truth to the people as best we can.

no one should put this many hours into work this is not good people should not work this hard i'm not they should not do this this is very painful painful in what sense it's because my earth's my brain and my heart particularly if you're starting a company you need to work super hard so what what does super heart mean well when my brother and i were starting our first company instead of getting an apartment we just rented us a small office and we slept on the couch and we we showered at the ymca and uh we're sohot up we had just one computer so the the website was up during the day and i was coding at night seven days a week all the time and i i briefly had a girlfriend that period and in order to be with me she has to sleep in the office so i work hard like i mean every waking hour that's that's the the thing i would i would say if you particularly if you're starting a company um and i mean if you do simple math say like okay if somebody else is working 50 hours and you're working 100 you'll get twice as doneas much done in the course of a year as the other company just work like hell i mean you just have to put in you know 80 hour 80 to 100 hour weeks every week and then a lot of work that all those things improve the odds of success um i mean if if other people are putting in 40 hour work weeks and you're putting in 100 hour work weeks then even if you're doing the same thing you know that in in one year you will achieve what they achieve you will achieve in four months what it takes them a year to achieve what was your biggestfailure and how did it change you we almost did diet spacex actually so we i'd budgeted for or three flights i mean technically i didn't have a plan where i had had the money from paypal i had like 180 million from paypal i thought you know i'll i'll allocate half of that to spacex and tesla and solarcity and um that should be fine i'll have 90 million likes just lots you know uh but but then what happened is um things cost more and took longer than than i thought so i had a choice of either put the rest of the money in or thecompanies are going to die and it's like so i ended up putting all the money in and borrowing money for rent from france um 2008 was brutal [Music] yeah 2008 we had the third consecutive failure of the falcon rocket for spacex um tesla almost went bankrupt we closed our financing round 6 pm christmas eve 2008. it was the last hour of the last day that it was possible we would have gone bankrupt two days after christmas otherwise spacex is alive by the skin of its teeth so is tesla um if things just gone a little bit the other way both companies would be dead and i had like one of the most difficult choices i ever faced in life was was in 2008 and i think i had like maybe 30 million dollars left or 30 or 40 million left in 2008 i had two choices i could put it all into one company and then the other company would definitely die um or split itbetween the two companies and but if i split up between two companies then both might die um and you know when you put your blood sweat and tears into creating something you're building something it's like a child and so it's like which one am i gonna let one starve to death i can bring myself to do it so i put i split the money between the two fortunately thank goodness uh they both came through tesla really faced the severe uh threat threat of death due to the model 3 production ramp essentially the company was bleedingmoney like crazy and and just if if we didn't solve these problems in a very short period of time uh we would die uh and was extremely difficult to solve them how close to death did you come we're within single budget weeks 22 hours a day or like what how many hours working yeah so seven days a week sleeping in the factory uh i worked everywhere from the i worked in the oaks in the paint shop general assembly body shop you ever worry about yourself imploding like just too much absolutely i think failure isbad um i don't think it's good but if if something's important enough then you you do it even though the risk of failure is high were you a little naive when you thought i'll just i can easily build build an electric car and a rocket i didn't think it would be easy um like i said i thought they would probably fail um but you know like creating a company is almost like having a child so it's sort of like how do you say your child should not have food so one once you have the company you have to feed it andannounce it and take care of it even if it it ruins you yeah [Music] but uh supposing there wasn't tough times in 2008 end of 2008 how did you get through that period of crisis can we just break for a second you wanna wait a little while yeah sure if it was worth it let me sure hope it was worth it well there's a ton of failures along the way that's for sure except for as i said for spacex the first three launches failed and uh we were just barely able to scrape together enough parts and money to do thethe fourth launch that fourth launch had failed we would have been dead so multiple failures along the way um i tried very hard to to get the right expertise in for for spacex i tried hard to to find a great chief engineer for the rocket but the good chief engineers wouldn't join and the bad ones well there was no point in hiring them so i ended up being chief engineer of the rocket so if i could have found somebody better than we would have maybe had less than three failures when you had that third failure in a rowdid you think i need to pack this in never why not i don't ever give up i mean i'd have to be dead or completely incapacitated you know there are american heroes who don't like this idea neil armstrong gene cernan have both testified against commercial space flight and the way that you're developing it and i wonder what you think of that i was very sad to see that because those guys are you know those guys are heroes of mine so it's really tough you know i i wish they would come and visit and and see the hardware thatwe're doing here and i think that would change their mind they inspired you to do this didn't they yes and to see them casting stones in your direction [Music] it's difficult did you expect them to cheer you on so they're hoping they would something that can be helpful is fatalism uh to some degree um if you just if you just accept the probabilities um then that diminishes fear uh so um starting spacex i thought the odds of success were less than 10 um and i just accepted that actually probably i would just lose lose everything umbut that maybe would make some progress if we could just move the ball forward even if we died maybe some other company could pick up the baton and move and keep moving it forward um so that we'll still do some good um yeah same with tesla i thought the odds of a car company succeeding were extremely low in creating these companies we thought that we would be successful i thought that the most likely outcome was failure but but it was still worth doing even though the odds of success were low in fact even for for sport spacexthe originally what i started doing was not creating a rocket company but but actually was going to do a small mission to mars which was just a philanthropic mission where you would send a small greenhouse with seeds and dehydrated gel in the wood upon landing hydrate the gel and you'd have this cool picture of green plants on a red background and the public tends to respond to precedence and superlatives so this will be the first life on mars furthest the life's ever traveled and you'd have this great money shot ofgreen plants on a red background so um i thought that would get people's attention so um but but the expectation for that was was no return so i thought we wouldn't get any uh you know just spend the money on that and it wouldn't wouldn't happen if you're creating a company or if you're joining company the most important thing is to attract is to attract great people so either you would join a group that's amazing that you really respect or if you're building a company you've got to gather greatpeople i mean all the company is is a group of people that have gathered together to create a product or service and so depending upon how talented and hard-working that group is and degree to which they are focused uh cohesively in a good direction that will determine the success of the company so do everything you can to to gather great people uh if you're creating a company then i'd say focus on on signal over noise a lot of companies get get confused they spend money on things that don't actually make the product betterso for example at tesla we've we've never spent any money on advertising we put all the money into r d and manufacturing and design to try to make the car as good as possible and i think that's that's that's the way to go so if for any given company just can keep thinking about are these efforts that people are expending are they resulting in a better product or service and if they're not stop those efforts starting a business i'd say number one is have a high paying threshold that's there's a friend of mine who'sgot a good saying which is that starting a company is like eating glass and stirring into the abyss okay that's um that's generally what happens because um when you first start a company there's lots of optimism and things that things are great and then so happiness at first is high then you encounter all sorts of issues uh and happiness will steadily decline and then you'll go through a whole world of hurt that's and then eventually you'll if you succeed and in most cases you will not succeedum and tesla almost didn't succeed came very close to failure um then if you succeed then after a long time you will finally get back to happiness you've got to make sure that that you that whatever you're doing is a great product or service it has to be really great and i go back to what i was saying earlier where if you're a new company i mean unless it's like some new industry or or new market that if it's an untapped market or then then uh you have more ability to you there's thisthe standard is lower for your product service but if you're entering anything where there's an existing marketplace against large entrenched competitors then your product or service needs to be much better than theirs it can't be a little bit better because then you put yourself in the shoes of the consumer and they say why would you buy it as a consumer you're always going to buy the trusted brand unless there's a big difference so a lot of times uh you know entrepreneur will come up with somethingwhich is only slightly better and it's it's not it can't just be slightly better it's got to be a lot better a well thought out critique of whatever you're doing is as valuable as gold [Music] and you should seek that from everyone you can but particularly your friends um usually your friends know what's wrong but they don't want to tell you because they don't want to hurt you it doesn't mean your friends are right but very often they are right and you at least want to listen verycarefully to what they say and to everyone if you're looking for basically [Music] you should take the approach that that you're wrong you know that that you the entrepreneur are wrong your goal is to be less wrong advice i'd give to people starting company to entrepreneurs in general is really focus on making a product that your customers love and it's so rare that you can buy a product and and you love the product when you bought it this is this is there are very few uh things that fit into that categoryand if you if you can come up with something like that your business will be successful for sure i think uh really an obsessive uh nature with respect to the quality of the product it is very important uh yeah so you know being obsessive compulsive is a good thing in this context really really liking what you do whatever area that you get into um given that you know even if you're if you're the best the best there's always a chance of failure so i think it's important that you really like whateveryou're doing if you don't like it life is too short um you know i'd say if and also if you if you like what you do and you think about it even when you're not working i mean it'll just it's it's something that your mind is drawn to and and if you don't like it you just really can't make it work i think when i was young i i didn't really know what i was going to do when i got older um people kept asking me and and um but but then eventually i thought that the idea of inventing thingswould be would be really cool and the reason i thought that was because i i read a quote from author c clock which said that a sufficiently advanced technology is indistinguishable from magic and and that's really true if you think if you go back say 300 years the things that we take a sufficiently advanced technology is indistinguishable from magic and and that's really true uh being able to see over long distances being able to communicate being able to see over long distances being able to communicate havingeffectively with with the internet in times past in fact i think it actually goes beyond that because there are many things that we take for granted today that weren't even imagined in times past they weren't even in the realm of magic so that it actually goes goes beyond that so i thought well you know if if i can do some of those things basically if i can advance technology then that that's like magic and that would be really cool um and the the i i always had sort of a slight existential crisis because i was tryingto figure out what does it all mean like what's the purpose of things and um i came to the conclusion that if if we can advance the this the knowledge of the world if we can do things that expand the scope and and scale of consciousness then we're better able to ask the right questions and become more enlightened and and that's really the only way forward so uh so so i i studied physics and business because i figured in order to do a lot of these things you need to know how the universe works and you need to know howhow the economy works and you also need to be able to bring a lot of people together to work with you to create something because it's very difficult to do something as as an individual if it's if it's a significant technology so i i originally came out to to california to try to figure out how to improve the energy density of of of electric vehicles basically to try to figure out if there was an advanced capacitor that that could serve as an alternative to batteries and that was in 95 and that's also when the internet started tohappen and and i i thought well i can either uh pursue this tech this technology where success maybe may not be one of the possible outcomes which is always tricky um or uh participate in the internet and and be part of it and and i think maybe it's helpful to say one of the things that was important then in the creation of paypal was was was kind of how it started because initially the initial thought was with paypal was to create an agglomeration of financial services so you have one place where all your financial services needs wouldbe seamlessly integrated and um and and work smoothly and then we had like a little feature which was to do email payments um and whenever we showed show the system off to someone uh we'd show the hard part which was the um the agglomeration of financial services which was quite difficult to put together nobody was interested um then we'd show people email payments which was actually quite easy and everybody was interested so we focused on email payments and really try to make that work and and that's what really got things totake off um but but if we hadn't if we hadn't responded to what people said then we probably would not have been successful so it's important to look for things like that and and focus on them when when you when you see them and you correct your prior assumptions going from paypal i thought it will what what are some of the the other problems that are likely to most affect the future of humanity um it really wasn't from the perspective of what what's the rank ordered best way to to make moneyum which which is which is okay but um it was really what i think is going to most affect the future humanity so the i think the the biggest terrestrial problem we've got is uh sustainable energy but the production and consumption of energy in a sustainable manner if we don't solve that this the sensory is the century we're we're in deep trouble um and then the the other one being the extension of life beyond earth to make life multi-planetary when i started spacex i it actually initially i thought that well there'sthere's no way one could possibly start a rocket company i i wasn't that crazy um but but then uh i thought well what is a way to um increase nasa's budget that was actually my initial goal so so obviously the financial outcome from such a mission would probably be zero um so anything better than that was on the upside so i actually went to i went to russia three times to look at buying um a refurbished icbm and uh i can tell you it was very weird going there in in 2000 late 2001 2002 going to the russianrocket forces and saying i'd like to buy two of your biggest rockets but you can keep the nuke and aft after making several trips to to russia i came to conclusion that that actually uh my initial impression was was wrong about uh because my initial thought was well that that there's not enough will to explore and expand beyond earth and have a mars base and that kind of thing but i can't conclusion that that was wrong um in fact there's plenty of will particularly in the united states because the united states is a nation ofexplorers of people who came here from from other parts of the world i think the united states really a distillation of the spirit of human exploration so after my third trip i said okay what we really need to do here is try to solve the space transport problem and uh and started spacex um and this was against the advice of pretty much everyone i talked to um one friend made me sit down and watch a bunch of videos rockets blowing up let me tell you he wasn't far wrong it was tough going there in the beginning because i'd never builtanything physical i mean i built like little model rockets as a kid and that kind of thing but um i'd never had a company that built any physical started to figure out how to how to do all these things and and bring together the right team of people we did all that and and then failed three times um it was tough tough going um because thing about a rocket is that the the passing grade is 100 you don't get to actually test the rocket in the real environment that it's going to be in so i think so the best analogy for forrocket engineering is it's like if you want to create a really complicated bit of software um you could you can't run the software as an integrated hole and you can't run it on the computer it's intended to run on but the first time you put it all together and write it on that computer it must run with no bugs that the first launch i was picking up bits of rocket near the launch site it was a bit sad we we learned with with each successive flight and and were able to with uh eventually with the fourth flight in 2008uh reached orbit and that was also with the last bit of money that we had so that's we got the falcon one two orbit and then uh began to scale that up to to the falcon 9 which is about an order of magnitude more a thrust it's around a million pounds of thrust and we managed to get that to orbit and then uh developed a dragon spacecraft uh which um recently was able to dock and return to earth from the space station so it's a huge relief i still can't quite believe it actually happened um but there's a lot more that that thatmust happen beyond this in order for humanity to be to become a space faring civilization ultimately a multi-planet species um and that's something i think it's it's it's vitally important and and i hope um that that some of you will will participate in in that either at spacex or at other companies because it's just really one of the the most important things for the preservation and extension of consciousness um it's worth noting as i'm sure people are aware that the earth has been around for fourbillion years and uh civilization at least in terms of having um writing has been around for 10 000 years and that's been generous um so uh it's it's really uh somewhat of a tenuous existence that that civilization and and consciousness as we know it has been on earth and i think um i'm actually i'm actually fairly optimistic about the future of earth so i don't want to i don't want to sort of people to have the wrong impression that i think we're all about to die i think i think we'll i think thingswill most likely be okay for a lo for a long time on earth but not not for sure but most likely um um but but even if it's if it's sort of 99 likely one a one percent chance it's still it's still worth uh spending a fair bit of effort to ensure that we have um we've backed up the biosphere you know planetary redundancy if you will um and uh and so i think i think it's really really quite important and in order to do that there's a breakthrough that needs to occur which is to create a a rapidly andcompletely reusable um transport system to mars um which which is one of those things that's right on the borderline of of of of of impossible um but that that's sort of the the thing that we're we're going to try to achieve that with with with spacex when i was a kid i was wondering kind of what's the meaning of life like why are we here what's it all about and um i came to the conclusion that uh what what really matters is trying to understand the right questions to ask and the more that we can increasethe scope and scale of human consciousness the better we are able to ask these questions so i think that there's certain things that are necessary to ensure that the future is good and some of those things are in the long term having long-term sustainable transport and sustainable energy generation and uh to be a space-bearing civilization and for humanity to be out there among the stars and be a multi-planetary uh species i mean i think being a multi-planet species and being out there among the stars is important foruh the long-term survival of humanity and uh that's one reason kind of like life insurance for life collectively life as we know it but then the part that i find personally most motivating is that it creates a sense of adventure and it makes people excited about the future and if you consider two futures one where we are forever confined to earth until eventually something terrible happens or another future where we are out there on many planets maybe even going beyond the solar system i think that second version isincredibly exciting and inspiring and there need to be reasons to get up in the morning you know life can't just be about solving problems otherwise what's the point there's got to be things that people find inspiring and make life worth living you're 47 what is the likelihood that you personally will go to mars 70 we've recently made a number of breakthroughs that i that i'm just really fired up about and when does that happen in our lifetimes yeah yeah i'm talking about moving there so it'slike so if you get the price per ticket maybe around a couple hundred thousand dollars this could be an escape hatch for rich people no if your probability of dying moz is much higher than earth really the africa going to mars would be like shackleton's after going to the antarctic it's going to be hard there's a good chance of death going in a little can through deep space you might land successfully once you land successfully there will be a map you'll be working non-stop to build the base series you'renot not much time for leisure and once you get there even after all this uh there's a very harsh environment to use a good chance you die there we think you can come back but we're not sure now does that sound like an escape patch for rich people and yet you would unhesitating like you know there's lots of people like climb mountains you know why they climb mountains because people die on endeavors all the time they like doing it for the challenge i think that the probable probable outcome for civilization ison earth is quite quite good for a long time um but i still think that we should try to extend life beyond earth and have a and the thing to do is to establish a base on mars and ultimate and try to make that a self-sustaining base as soon as possible um so uh i don't expect that spacex is going to do that sort of single-handedly but i think we're we're gonna try to advance the technology of space travel to the point where we can at least send some number of people to mars which is not currently possible on the tesla frontthe goal with tesla was really to try to show that what electric cars can do because people had the wrong impression we had to change people's perception of an electric vehicle because they used to think of it as something that was slow and ugly and had low range kind of like a golf cart um and and so that's why we created the tesla roadster to show that you can be fast um attractive and and long range um and it's amazing how even though you can show that something works on paper you know and the calculations are veryclear until you actually have the physical object and they can they can drive it it doesn't really sink in for people um and so that that i think is is something worth noting if you're going to create a company the first thing you should try to do is create a working prototype um you know everything everything looks great on powerpoint you can you can make anything work on powerpoint but if you have if you have an actual demonstration article even if it's in primitive form that's much much more effective forconvincing people now is the time to overrule this administration's pledge to mediocrity listen tesla's to sell sell sell you don't want to own this stock you shouldn't even rent the dorn thing why because beyond the hype there's just not much going on here tesla still has yet to turn a profit there'll be a 1.5 billion dollar company with no profit his most recent quarter actually lost more money than it did the year before 1.5 billion losing more money than you before this is a company with limited visibility you put 90 billion dollars like 50 years worth of breaks into into solar and wind to to solyndra and fisker and tesla and enter one i mean i had a friend who said you don't just pick the winners and losers you pick the losers private enterprise will not ever lead a space frontier not because i don't want them to but my read of history history tells methey can't it's not possible one of the biggest mistakes people generally make and i'm guilty of it too is wishful thinking you know like you want something to be true even if it isn't true um and so you ignore the things that you ignore the real truth because of what you want to be true this is a very difficult trap to avoid and like i said certainly one that i find myself in having problems with but if you just take that approach of you're always to some degree wrong and your goal is to be less wrong andand solicit critical feedback particularly from friends like friends particularly friends if somebody loves you they want the best for you they don't want to tell you the bad things um so you have to ask them okay you know and said really i really do want to know um if you were 22 today what with the five problems that you would think about working on b um well first of all i think um if somebody is doing something that is useful to the rest of society i think that's a good thing like it doesn't have tochange the world like you know if you're doing something that has high value to people um and frankly even if it's something if it's like um just a little game um or you know the some improvement in photo sharing or something if it if it has a small amount of good uh for a large number of people um that's i mean i think that's that's fine like stuff doesn't need to be changed the world just to be good um uh but you know in terms of things that i think are most likely to affect the the futureof humanity i think um ai is probably the single biggest item in the near term that's likely to affect uh humanity so it's very important that we have the advent of ai uh in a good way that that is something that um if you if you could look into the crucible and see the future you would like you would like that outcome um because it is something that could go um could go wrong um as we've talked about many times um and so we really need to make sure it goes right um that's that's i think ai working on ai and making sure it's agreat future that's that's the most important thing i think right now um the most pressing item sec uh then um obviously anything to do with with genetics um if you can actually solve genetic diseases if you can prevent dementia or alzheimer's or something like that that with genetic reprogramming that would be wonderful so i think this genetics it might be the sort of second most important item i think um having a high bandwidth interface to the brain like um we're currently bandwidth limited we have a digital tertiary self in theform of our email capabilities like computers phones applications uh we're effectively superhuman but we're extremely bound with constrained in that interface between the cortex and your sort of uh that tertiary digital form of yourself and helping solve that bandwidth constraint would would be i think very important for the future as well what have you done or what did you do when you were younger that you think sort of set you up to have a big impact well i think first of all i should say that i do not expect to be involved inall these things so the the the the five things that i thought about the time in in college quite a long time ago uh 25 years ago you know being you know making life multi-planetary um selling accelerating the transition to sustainable energy um the the internet broadly speaking um and and then genetics and ai i think um i didn't expect to be involved in in in all of those things i actually at the time in college i sort of thought um helping with electrification of cars was how i would start out and that's uh that's actually what i workedon as an intern was um advanced uh ultra capacitors with to see if there would be a breakthrough relative to batteries for energy storage and cars and then when i came out to go to stanford um that's what i was going to be doing my grad studies on is this was working on advanced energy storage technologies for electric cars and i put that on hold to start an internet company in 95 because um there does seem to be like a time for particular technologies uh when they're at a steep point in the inflection codeand um and i didn't want to you know do a phd at stanford and then and watch it all happen um and then and i wasn't entirely certain that the technology i'd be working on would actually succeed um i can get you can get a you know doctrine on many things that ultimately are not do not have a practical bearing on the world um and i wanted to you know just i really was just trying to be useful that's the optimization it's like what what what can i do that would actually be useful how should someone figure out howthey can be most useful whatever this thing is that you're trying to create what would what would be the utility delta compared to the current state of the art times how many people it would affect so that's why i think having something that has that that has a makes makes a big difference but affects a sort of small to moderate number of people is great as is something that makes even a small difference but it but affects a vast number of people when you're trying to estimate probability of success sothis thing will be really useful good area under the curve i guess to use the example of spacex when you made the go decision that you were actually going to do that this was kind of a very crazy thing at the time very crazy for sure yeah i'm not sure about saying that but i kind of agree i agreed with them that it was quite crazy crazy if um if the objective was um to achieve the um best risk adjusted return um starting our company is insane um but that was not that was not my objective i i i'd simply come to the conclusion umthat if something didn't happen to improve rocket technology would be stuck on earth forever and um and the big aerospace companies had just had no interest in radical innovation um all they wanted to do was try to make their old technology slightly better every year and in fact um sometimes it would actually get worse um and particularly in rockets is pretty bad like the in in 69 we were able to go to the moon with a saturn v and then the space shuttle could only take people to low earth orbit and then the space shuttle retired and that thattrend is basically trends to zero um if you also think technology just automatically gets better every year but it actually doesn't it only gets better if smart people work work like crazy to make it better that's how any technology actually gets better and by itself technology if people don't work in it actually will decline um i mean you can look at the history of civilizations many civilizations and look at say um ancient egypt where they were able to build these incredible pyramids and then theybasically forgot how to build pyramids um and and then even hieroglyphics they've forgotten how to read hieroglyphics so we look at rome and how they're able to look to build these incredible roadways and aqueducts and indoor planning they've got how to do all of those things and um there are many such examples in in history um so i i think um should always bear in mind that you know entropy is not on your side you may have heard me say that it's good to think in terms of the physics approach or first principlesuh which is [Music] rather than reasoning by analogy you boil things down to the most fundamental truths you can imagine and you reason up from there and this is a good way to figure out if if something really makes sense or if it's just what everybody else is doing it's hard to think that way you can't think that way about everything it takes a lot of effort but if you're trying to do something new it's the best way to think and that framework was developed by by physicists to figure out counterintuitive thingslike quantum mechanics so it's really a powerful powerful method how do you think about making a decision when everyone tells you this is a crazy idea or where do you get the internal strength to do that well first of all i'd say i actually think i think i feel feel fear quite strongly um so it's not as though i just have the absence of fear i've i feel it quite strongly um but there are just times when something is important enough you believe in it enough that you you do it in spite of fear people should think well i feelfear about this and therefore i shouldn't do it um it's normal to be to feel fear like you'd have to definitely something mentally wrong if you didn't feel fear if you have an advice to them young people globally who want to be like elon musk what's your advice to them i think that probably they shouldn't want to be you it i think it sounds better than it is okay yeah it's uh not as much fun being me as you'd think i don't know you don't think so yeah there's definitely it could be worse forsure but it's um i i'm not sure i would i'm not sure i want to be me so when everybody leaves it's just elon sitting at home brushing his teeth just bunch ideas bouncing around your head when did you realize that that's not the case with most people i think when i was i don't know five or six or something i thought i was insane it was just strange because it was clear that other people do not what their mind wasn't exploding with ideas i was like hmm i'm strange i don't think i don't think you'dnecessarily want to be me people would like it that much it's very hard to turn it off it's like a neverending explosion all the time what do you think the odds of the mars colony are at this point today um oddly enough i actually think they're pretty good at this point i am certain there is a way i'm certain that success is one of the possible outcomes for establishing a self-sustaining mars colony in fact growing mars colony i'm certain that that is possible whereas until maybe a few years ago i was notsure that success was even one of the possible outcomes it's a meaningful number of people going to mars i think this is potentially something that can be accomplished in about 10 years maybe sooner maybe nine years i need to make sure that spacex doesn't die between now and then and that i don't die or if i do die that someone takes over who will continue that you shouldn't go on the first launch yeah exactly the best of the available alternatives that i can come up with and maybe somebody else can come up with a betterapproach or better outcome is that we achieve democratization of ai technology meaning that no one company or a small set of individuals has control over advanced ai technology i think that that's very dangerous it could also get stolen by somebody bad you know like some evil dictator or country could send their intelligence agency to go steal it and gain control it just becomes a very unstable situation i think if you've got any um any incredibly powerful ai you just don't know who's who's going tocontrol that so it's not as i think that the risk is that the ai would develop a will of its own right off the bat i think it's more it's the consumers that some someone um may use it in a way that is bad um or and even if they weren't going to use it in a way that's bad that somebody could take it from them and use it in a way that's bad that that i think is quite a big danger so i think we must have democratization of ai technology and make it widely available um and that's you know the reason thatobviously uh the rest of the team uh you know created open ai was to help uh with the democracy help help spread out ai technology so it doesn't get concentrated in the hands of a few and but then of course that needs to be combined with solving the high bandwidth interface to the cortex um humans are so slow humans are so slow yes exactly but you know we already have a situation in our brain where we've got the cortex and the limbic system and the limbic system is kind of i mean that's that's the primitive brain it'skind of like the urine your instincts and um whatnot and then the cortex is the thinking upper part of the brain those two seem to work together quite well um occasionally your cortex and limbic system may disagree generally works pretty well and it's like rare to find someone who i've not found someone who wishes to either get rid of their cortex or get rid of their living system so i think if if we can effectively uh um merge with uh ai by um improving that the the neural link between your cortex and thethe your digital extension yourself which already likes it already exists just has a bandwidth issue um and then then effectively um you become an ai human symbiote um and and if that then is widespread with anyone who wants it can have it uh then we solve the control problem as well um we don't have to worry about um some sort of evil dictator ai um because kind of we are the ai um collectively that seems like the best outcome i can think of i think we've got a really talented group with opening eye yeah really really talentedteam and they're working hard open a is structured as uh see a 51c3 nonprofit um but you know many non-profits uh do not have a sense of urgency it's fine they don't have to have a sense of urgency um but open ai does um because i think people really believe in the mission i think it's important um and it's it's about minimizing the risk of existential harm in the future and uh so i think it's going well i'm pretty impressed with what people are doing and the talent level and obviously we're always looking forgreat people to join when i interview somebody i really just ask them to tell me the story of their career and what they you know what are some of the tougher problems that they dealt with how they dealt with those and how they made decisions at key transition points and usually that's enough for me to get a very good gut feel about someone and what i'm really looking for is evidence of exceptional ability so did they face really difficult problems and overcome them um and and then of course you want to make surethat that if there was some significant accomplishment were they really responsible or somebody else more responsible and usually the person who's had to struggle with the problem they really understand it you know and they don't forget you know if it was very difficult so you can ask them detailed very detailed questions about it and they will they'll know the answer whereas the person who was not truly responsible for that accomplishment uh will not know the details there's no need even to havea college degree at all or even high school i mean if somebody graduated from a great university that may be indeed that may be an indication that they will be capable of great things but it's not necessarily the case um you know if you look at say people like bill gates or larry ellison steve jobs these guys didn't graduate from college but if you had a chance to hire them of course that would be a good idea so you know just looking just for evidence of exceptional ability and if there's a track record ofexceptional achievement then it's likely that that will continue into the future what sort of things do you look for in people or in processes that make the workforce better well i think the massive thing that can be done is to make sure your incentive structure is such that uh innovation is rewarded and lack of innovation is punished there's got to be a characteristic so if somebody is innovating um and doing making good good progress then they should be promoted sooner um and if somebody is completely failing to innovate um notevery role requires innovation but if they're in a role where innovation is should be happening and it's not happening then they should either not be promoted or exited and let me tell you you'll get promote you could you'll you'll get innovation real fast does that carrot and stick approach help uh do you think people be more risk averse or less risk averse when trying different things you've got to have some acceptance of failure failure must be an option if failure is not an option it's going to result inextremely conservative choices and you may not may get something even worse than lack of innovation things may go backwards what you really want is you want reward and punishment to be proportionate to the actions that you seek so if uh if what you're seeking is innovation then you should reward success and innovation um and only there there should be minor consequences for lack of minor consequences for for trying and failing should there should be minor with significant rewards for trying and succeeding minor consequences for trying and notsucceeding um and big and major negative consequences for not trying if you have that incentive structure you will get innovation like you can't believe the purpose of neural link like what do we what's our goal our goal is to solve important spine and brain problems with a seamlessly seamlessly implanted device so you want to have a device that you can basically put in your head and feel and look totally normal but it solves some important problem in your brain or spine so going into the neural link architecturewhat we've done over the past year is dramatically simplify the device so we we about a year ago we had a device which uh had multiple parts including a piece that it had to sort of sit behind your ear and it was it was it was complex and you and you wouldn't still look totally normal you'd have a thing behind your ear so um we've simplified this to simply something that is about the size of a large coin um and it it goes uh in your skull replaces a piece of skull um and the wires uh then then connectuh within a few centimeters or about an inch away from the device um and this is sort of what it looks like this is a little device i mean frankly to to sort of simplify this uh what we're i mean it's more than this but it's in a lot of ways it's kind of like a fitbit in your skull with tiny wires our current prototype version 0.9 has about a thousand channels so that's about 100 times better than the the next best consumer device that's available and it's a 23 millimeters by eight millimeters it actually uh fits quite nicely in your skull just your skull is about 10 millimeters thick so it fits it goes flush with your skull it's invisible and all you can see afterwards is there's a tiny scar and if it's under your hair you can't see it at all in fact i could have a neural link right now and you wouldn't know it's also inductivelycharged so it's charged in the same way that you cho you charge a smart watch or a phone um and so you can use it all day uh charge it at night and have full functionality so you would really um you know it would be completely seamless and yeah no wires uh in terms of getting a link so that we you need to have the device a a great device and you also need to have a great robot that puts in the electrodes and it does the surgery so you want the surgery to be as as automated and as possible and the only way you canachieve the level of precision that's needed is with an advanced robot the link procedure the the installation of a link done in under an hour so you can basically go in the morning and leave the hospital in the afternoon and it can be done without general anesthesia so this is our surgical robot and we actually ultimately want this robot to do essentially the entire surgery uh so in everything from from incision uh removing the the skull inserting the electrodes placing the device um and then um closing things up andhaving you ready to leave so we want to have a fully automated system how do you spend your days now like what what do you allocate most of your time to my time is mostly split uh well between spacex and and tesla and of course i try to spend um it's a part of every week at open ai so i spend most i spend basically half a day at openai most weeks and then and then i have some opening stuff that happens during the week i think a lot of people think i must spend a lot of time with media or or on businessy things but actuallyalmost uh almost all my time like 80 of it is spent on engineering design in engineering and design so it's um developing next generation product that's 80 of it i think a lot of people think i'm kind of a business person or something which is fine like business is fine but um like i uh but really it's you know it was like it's spacex uh gwen shotwell is chief operating officer she kind of manages um uh legal finance um sales um and kind of general business activity and then my time is almost entirely with theengineering team working on improving the falcon 9 and the dragon spacecraft and developing the most colonial architecture i mean at tesla it's working on the model 3 and you know some in the design studio typically have a day week dealing with aesthetics and and look and feel things and and then most of the rest of the week is just going through engineering of of the car itself as well as engineering of the the factory um because the the biggest epiphany i've had is that what really matters is the is the machine that builds themachine the factory um and this that is at least towards magnitude harder than the vehicle itself what are the scenarios that scare you most humanity really is not evolved to think of existential threats in general we're involved to think about things that are very close to us near term to to be upset with other humans and not not to really to think about things that could destroy humanity as a whole but then in recent decades recent just really in the last century we had nuclear bombs which are could potentially destroycivilization obviously we have ai which could destroy civilization uh we have global warming which could destroy civilization or at least severely disrupt uh civilization um and excuse me how could ai destroy civilization you know it would be something the same way that humans destroyed the habitat of primates i mean it wouldn't necessarily be destroyed but we might be relegated to a small corner of the world when homo sapiens became much smarter than other primates i pushed all the other ones into small habitatscouldn't ai even in this moment just with the technology that we have before us be used in some fairly destructive ways you could make a swarm of assassin drones for very little money by just taking the the face id chip that's used in cell phones and uh having a small explosive charge and a standard drone and have them just do a grid sweep of the building until they find the person they're looking for ram into them and explode you can do that right now no extra no new technologies needed right now people just think this stuff is of ofsci-fi novels and movies and it's so far away but every time i hear you speak it's like well no this stuff is sitting it's right here probably a bigger risk than being hunted down by a drone is that uh ai would be used to make incredibly effective propaganda that would not seem like propaganda so these are deep fakes yeah influence the direction of society influence elections artificial intelligence just hones the message holds the message check looks the feed looks at the feedback makes this message slightly betterwithin milliseconds it could it can adapt its message and shift and react to news and there's so many uh social media accounts out there that are not people they can't how do you know it's a first another person people look like they have a much better life than they really do people are posting pictures of when they're really happy they're modifying those pictures to be better looking even if they're not modifying the pictures they're at least selecting the pictures for the best lighting the bestangle so people basically seem uh they're way better looking than they basically really are um and they're way happier seeming than they really are so if you look at everyone on instagram you might think man they're all these happy beautiful people and i'm not that good looking and i'm not happy so i'm a suck you know and that's gonna make me feel sad when in fact those people you think are super happy actually not that happy some of them are really depressed they're very sad some of the happiest seeming peopleactually some of the saddest people in reality so i think i think things like that can make people quite sad this may sound corny but love is the answer wouldn't hurt to have more love in the world i think you know i think people should be nicer to each other and give people and give give more credit to others and don't assume that they're mean until you know they're actually mean you know just it's easy to demonize people you're usually wrong about it people are nicer than you think give people more creditthere's going to be some amount of failure but you want your net output that useful output to maximized failure is essentially irrelevant unless it is catastrophic the final thing i would encourage you to do is now is the time to take risk as you get older your obligations increase so and once you have a family you start taking risk not just for yourself but for your family as well it gets much harder to do things that might not work out so now is the time to do that before you before you have those obligations so i would encourage you totake risks now do something bold you won't regret it.

i think that probably they shouldn't want to be you i think it sounds better than it is okay um yeah it's uh not as much fun being me as you'd think i don't know you don't think so there's definitely it could be worse for sure but it's um i i i'm not sure i would i'm not sure i want to be me okay but if you know i think advice i mean if you want to make progress in things i think that um but the best analytical framework for understanding the future is physics i'd recommend studyingthe uh the thinking process around physics like not just not not the equations i mean equations certainly they're helpful but the the the way of thinking in physics is the it's the best framework for understanding things that are counterintuitive and you know always taking the position that you are some degree wrong and your goal is to be less wrong over time one of the biggest mistakes people generally make and i'm guilty of it too is wishful thinking you know like you want something to be true even if it isn't true umand so you ignore the things that uh you ignore the real truth because of what you want to be true um this is a very difficult trap to avoid and like i said certainly one that i find myself in having problems with but if you just take that approach of you're always to some degree wrong and your goal is to be less wrong and and solicit critical feedback particularly from friends like friends particularly friends if somebody loves you they want the best for you they don't want to tell you the bad things so you have to ask themyou know and said really i really do want to know and then they'll tell you you don't need college learning learn stuff okay everything is available basically for free you can learn anything you want for free it is not a question of learning um there there is a value that colleges have which is like you know seeing whether somebody's is can somebody work hard at something including a bunch of sort of annoying homework assignments and still do their homework assignments uh and and kind of soldier through andand get it done you know that's that's like the main value of college and then also you know if you you probably want to hang around with a bunch of people your own age for a while instead of going right into the workforce um so i think colleges are basically for fun and to prove you can do your chores but they're not for learning i think failure is bad um i don't think it's good um but if if if something's important enough then you you do it even though the risk of failure is high um and and so i think my advice if somebodywants to start a company is they should bear in mind that the most likely outcome is is that it's not going to work and they should reconcile themselves to that past strong possibility and they should only do it if they feel that they they're they're really compelled to do it you know um because it's it's it's gonna the way starting company works is like usually in the beginning it's the very beginning it's kind of fun um and then it's really hellish for a number of years you talked about chewingglass yeah there's there's a friend of mine who's a successful entrepreneur and started actually his career around the same time as i did and he has a good good good phrase his name's bully uh um he said yeah you're starting companies like eating glass and staring into the abyss and you agree with that generally true um yeah and and and if you don't eat the glass you're not going to be successful also if you want to have more self belief and more self confidence i've created a special free program whereevery day for the next 254 days i will send you an unlisted video to help you boost your self-belief and self-confidence the link to join for free is in the description below there are just times when something is important enough you believe in it enough that you you do it in spite of fear when you want to do something new you you have to you have to apply the the physics approach well we have a lot of good good people at spacex that um a lot of really talented people uh in fact i wonder like sometimes how we can make use of their talentsin the best way because you know i think we're often not using their talents in the best way um yeah but you know to the point of the question i was just asked i want to make sure tesla recruiting does not have anything that says requires university because that's absurd but there is a requirement of evidence of exceptional ability like you just can't if you're trying to do something exceptional they must have evidence of exceptional ability i don't consider going to college evidence of exceptional abilityin fact ideally you dropped out and did something i mean obviously you know we just look at like you know gates is a pretty smart guy he dropped down john was pretty smart he dropped out you know larry ellison a smart guy he dropped out i'm like obviously not needed so did shakespeare even go to college probably not but one of the fun things for me is watching the the cargo go into the crew vessel you know all of a sudden we had dragon one now we have crew dragon and it's substantially different but familiar so tell us like what's beensome of the hardest parts to transition from cargo into crew because crew is a little more important than than cargo yes i mean cargo can be replaced crew cannot um and so the the level of scrutiny the level of attention is i mean i don't know order of magnitude greater it was it was it was already high for cargo i mean and and uh but it's it's just a whole nother level for for a crew um so you know after and after i told the spacex team that you know the uh this mission reliability is not really the top priority it is the only priorityright now um so we're just doing continuous uh engineering reviews uh from now non-stop uh 24 hours a day until launch just gone over everything again and again and again and i was out at the pad just recently just walking down the rocket um we've got a team that's just crawling over the rocket in the horizontal then we're gonna rotate it vertical then we're gonna crawl all over in the vertical and we're just looking for any any possible action that can improve the probability of success no matter howsmall whether that comes from an intern or me or anyone it doesn't matter what sort of things do you look for in people or in processes that make the workforce better sure well i think the massive thing that can be done is to make sure your incentive structure is such that innovation is rewarded and lack of innovation is punished they've got to be a characteristic so uh if somebody is innovating um and doing making good progress then they should be promoted sooner and if somebody is completely failing to innovatenot every role requires innovation but if they're in a role where innovation is should be happening and it's not happening then they should either not be promoted or exited and let me tell you you'll get promoted you could you'll you'll get innovation real fast so now your actual total mass of a steel uh of a reusable steel spacecraft is less than that of the most advanced carbon fiber vehicle you could possibly imagine yeah wow but this happened by accident by the way this may sound like some great insight but itactually happened because we were moving too slowly on composite um and i was like we cannot move this slowly or we'll go bankrupt so just do this with steel so yeah i mean the design has to be focused on problem solving otherwise you're going to spend too much time trying to figure you don't start with a yeah yeah i'm like sort of taken to management management by rhyming if the schedule is your schedule is long your design is wrong right this is very true that's good good point yes advice i'd give to people startingcompany to entrepreneurs in general is um really focus on making a product that your customers love um and it's so rare that you can buy a product and and you love the product when you bought it this is this is there are very few uh things that fit into that category and if you can come up with something like that your business will be successful for sure tesla really faced the severe uh threat of death due to the model 3 production ramp essentially the company was bleeding money like crazy and and just if if we didn't solve these problems ina very short period of time we would die and it was extremely difficult to solve them how close to death did you come we were within single-digit weeks 22 hours a day like what how many hours working yeah seven days a week sleeping in the factory uh i worked away from the i worked in the paint shop general assembly body shop you ever worry about yourself imploding like it's just too much absolutely no one should put this many hours into work this is not good and people should not work this hard i'm not they should not do this this is verypainful painful in what sense it's because my ears my brain and my heart so it's this is not recommended for anyone i just did it because if i didn't do it then tesla good chance as it would die the real way i think you you actually achieve intellectual property protection is by innovating fast enough if your rate of innovation is high then you don't need to worry about protecting vip because other companies will be copying something that you did years ago and that's fine you know just make sureyour rate of innovation is fast um speed is really the speed of innovation is what is what matters um and i do i do say this to my teams like quite a lot that innovation per unit time as i go innovation per year if you're what i say like is is what matters not innovation absent time because if you wanted to make say um 100 improvement in something and that took 100 years or one year that's radically different so it's like what is your rate of innovation that that matters and is the rate of innovation is that accelerating or deceleratingum and a weird thing happens when companies get big is that most companies or organizations the bigger they get they tend to get less innovative not just less innovative on a per person basis but less innovative in the absolute and i think this is probably because the incentive structure is not uh is not there for innovation um it's not enough to use words to encourage innovation the incentive structure must be aligned with that that's fundamental you need to work if depending on how well you want to do andparticularly if you're starting a company you need to work super hard so what what does super hard mean well when my brother and i were starting our first company instead of getting an apartment we just rented a small office and we slept on the couch and we we showered at the the ymca and uh we're so hot up we had just one computer so the the the website was up during the day and i was coding at night seven days a week all the time and i i sort of briefly had a girlfriend in that period and in order to be with me she had to sleep in theoffice so i work hard like it i mean every waking hour that's that's the the thing i would i would say if if you're particularly if you're starting a company um and i mean if you do simple math say like okay if somebody else is working 50 hours and you're working 100 you'll get twice as done as much done in the course of a year as the other company i think of the these things is just there's a certain amount of time and within that time you want the the best net outcome so for you know all the set of actions that youcan do there's going to be uh and some of which will fail some which will succeed and you want the the net useful output of your set of actions to be the highest so um i'm going to like use like a baseball analogy like you know baseball they don't let you just sit there and wait for the perfect pitch until that you get a real easy one they didn't give you three shots and the third one they say okay they get off the go back to the put somebody else up there um so these your three strikes on on baseballum not you know not only bad anymore so so you're what you're really looking for is like what's the batting average you know how how are you doing on uh on score um and just there's going to be some amount of failure but you want your net output um that useful output to be maximized failure is essentially irrelevant unless it is catastrophic don't just follow the trend so um you may have heard me say that it's good to think in terms of the physics approach of first principles which is rather than reasoning by analogy youboil things down to the most fundamental truths you can imagine and you reason up from there and this is a good way to figure out if if something really makes sense or if it's just what everybody else is doing it's hard to think that way you can't think think that way about everything it takes a lot of effort uh but if you're trying to do something new it's the best way to think um and that framework was developed by by physicists to figure out counterintuitive things like quantum mechanics soit's really a powerful powerful method we need to push for radical breakthroughs um and if you don't push for radical breakthroughs you're not going to get radical outcomes um and that that does mean taking risks um and yeah common sense that the the if you take a big risk in order to have a big reward there must be a big risk it's most the time you cannot find a big reward for small risk that's why those are rare so you're going to have some proportionality with the risk and reward i simplify your product as much aspossible um you know and then like if i think of some of the ways which how does a smart engineer make dumb mistakes including you know is optimize something that shouldn't exist don't optimize something that shouldn't exist um but people are trained to do this in college you can't say no to the professor you know the professor's going to give you the exam and you've got to answer all the questions or they'll get angry so and give you a bad grade so then you you always optimize the you always answer thequestion a lot of the times you should say this is the wrong question right in fact the question is definitely wrong to some degree just how wrong and i think just generally taking the approach that your design is some degree wrong probably a lot more than you think your goal is to make it less wrong over time zip2 started off as basically like say we're trying to figure out how to how to make enough money to exist as a company and the so so since there wasn't really any advertising money being made we thought we could umhelp existing companies get online bring their stuff online so we developed software that helped bring um love in newspapers and media companies online because they alone just didn't they also didn't know what the internet was they were big customers didn't you yeah and even the ones that were aware of the internet didn't have a software team so they could they weren't very good at developing functionality um and uh so we had as um investors and customers uh the new york times company night reader host and and so we wereable to get them to pay us to develop software for them to bring them online so online publishing stuff we did maps and directions and yellow pages and white pages and various other things 2008 in particular was was was awful because we had the third launch failure in a row of of our falcon 1 vehicle at spacex um and um we the tesla financing round that we were raising fell apart um because the economy is going to tailspin um and it's pretty hard to raise money for a startup car company uh you know late 2008 when gm and chrysler are busygoing bankrupt um that was that was tough and then solar city had to deal with uh morgan stanley and morgan stanley had to renege on the deal because they themselves were running out of money um so it looked like all three companies were gonna die and i was also going through divorce so that was definitely a low point so it's 2008 you're going through a divorce which like some to borrow your word douchebag vloggers are writing about to make even worse right yes that's true um in addition to all that stuff happening i was gettingdumped on massively in the press right yeah you're you know it looks like all three companies yeah are going to file i mean why do you keep going with all three like i feel like even a lot of great entrepreneurs in that situation would have been like i've already sunk everything i have in these companies and i gotta pick one but you didn't i mean you kept doing all three why um yeah that was that was a very tough call um at the end of 2008 that was that was probably the top you know one of the toughest goals i've had to makebecause i could either um reserve capital for one company or the other i mean before solar city didn't need a ton of capital so they were okay um but between spacex and and and tesla um you know it's sort of like like you've got two kids and what do you do do you spend all your money to to maximize probably the success of of one or do you do you try to keep both left unfortunately it worked how aside from making great products how do you get people excited about tesla there's a lot of people i know and that i talk to whoare just intrigued and interested and excited about tesla as a company the thing i really focus on at tesla is like we really put all of our money into an attention to trying to make the product as compelling as possible so um because i think that really the way to um sell any product is through word of mouth so if if one somebody gets the car they really like it they and and actually the key is like to have a product that people love um and and general people um you know if that a party or tour friends or whatever um you'll talkabout the things that you love but you know if you just like something it's okay you're not going to care that much but if you look at the reactions from the highs and the lows you're gonna yeah you're gonna talk you know and and then that'll generate work turns word of mouth that's basically how how our sales have have grown like we don't we're not spending money on advertising or endorsements or uh and um so anyone like buys our car they just water because they they like the car zip2 happened you sold it andit and bought the mclaren and if i'm not mistaken you you invested most of that money into your next uh your next venture uh x.com that's right um so uh yeah most of most of the funds went into x.com which was later renamed paypal that worked out pretty well [Music] it worked out pretty well but looking looking back on it um would because you put a lot of your eggs in in that basket would you would you advise entrepreneurs to roll the bones quite the way you did yeah absolutely i think so um i think i think i think it's worth investing yourown capital in what you do i don't believe in the sort of other people's money thing um you know i think if you're not willing to put your own assets at stake then you shouldn't ask other people to do that to do that we don't think too much about what competitors are doing yeah um just because i think it's important to um be just focused on making the best possible uh products um you know it's sort of maybe analogous to what they say about you know if you're in a in a if you're if you're in a in arace um don't worry about what how the what the other runners are doing just run what is the likelihood that you personally will go to mars 70 we've recently made a number of breakthroughs that i that i'm just really fired up about and when does that happen in our lifetimes yeah yeah i'm talking about moving there so it's like so if it can get the price per ticket maybe around a couple hundred thousand dollars this could be an escape hatch for rich people no if your probability of dying on marsis much higher than earth really the app would go to mars would be like shackleton's after going to the antarctic it's going to be hard there's a good chance of death going in a little can through deep space you might land successfully once you land successfully there will be a map you'll be working non-stop to build the base uh series you're not not much time for leisure and once you get there even after doing all this there's a very harsh environment to use a good chance to die there we think you can come back but we're notsure now does that sound like an escape patch for rich people and yet you would unhesitate and wake up you know there's lots of people like climb mountains you know why they climb mountains because people die on endeavors all the time they're like doing it for the challenge it's very important to to seek out to actively seek out and listen very carefully to negative feedback and this is something that people tend to avoid because it's it's painful yeah um but but i think this is a very commonmistake is to to not actively seek out and listen to uh negative feedback what do you do that you go into forums do you go into twitter like what are your areas where you go to look for feedback on let's say the tesla well it's like everyone i talk to is um in fact when um when friends get a product i say look i don't tell me what you like tell me what you don't like right um and and because otherwise your friend is not going to tell you what he doesn't like right this girl's going tosay oh i love this and that and and then and leave out the this is the stuff i don't like list because too much of your friend want you know it doesn't want to offend you so um so you really need to [Music] to to to to sort of coax negative feedback um and you should you know that if somebody is your is your friend or at least not your enemy and they're giving you negative feedback [Music] then they may be wrong but it's coming from a good place and sometimes even your enemies give you good negative feedback as a kid i didn'treally have any grand designs i mean the reason i started programming computers is because i like computer games and i play lots of computer games and i learned that if i wrote software and sold it then i could get more money and buy better computers so it wasn't really you know with some grand vision or anything um when i was growing up and i'd read lots of books and uh they were very often set in the united states and it seemed like a lot of new technology was being developed in the united states so i i thought okay ireally want to work on new technology so i want to get to silicon valley um you know which when i was growing up silicon valley seemed like some sort of mythical place uh you know like mount olympus or something i came to conclusion that my initial premise was was wrong uh that in fact the um there's there's a great deal of will uh you know that there's there's not such a shortage um but people don't think there's a way um and and that if people thought there was there was a way or do something thatwouldn't break the federal budget um then then people would support it which in retrospect i think is actually kind of obvious because um the the united states is a distillation of the human spirit of exploration people came here from other places um i mean it's you know there's no nation there's no i mean there's no nation that that's more a nation of explorers than united united states but but people need to believe that it's possible and it's that it's not you know it's they're not going togive up like healthcare or something important it's just it's got to be that that that's important so so i thought okay well then it's not a question of well it's a question of showing that there's a way in the beginning nobody wanted a tesla i can tell you that the the the when we made the original sort of roads to sports car uh people were like why would i want an electric car that's my gasoline car works fine i'm like no electric car is better i should try it um and it was you know hard to get peopleto do a test drive first one nobody knew who we were and then we heard this company and like yeah we're named after nikola tesla you know that guy nope um so for sure we were doing push in the beginning because people said there was no one telling us that they wanted an electric car so it was not it was not out of like you know it was like lots of people coming up to me saying hey i really want an electric car i heard that zero times um some people like it's like man we're gonna make an electric car and show thatthese things can be good um and then people want them um you know it's like i think it's like henry ford said that like the you know we're talking about the model t it's like if you ask the public what they wanted they'd say a faster horse so if you did like a big survey and say well hey public before automobiles what would you like it's like well i'd like my horse to go three miles an hour faster and eat less food and uh you know be stronger and live longer and that kind of thing um there will be basically a bunch ofincremental improvements on horse um because you learn when you say like what about an automobile that car that drives itself like what are you talking about that's not that sounds crazy but when you actually make an automobile and give it to people and say okay now this is a horse where you can keep it in the barn and if you leave for a month it's still alive yeah uh so carry more more weight than a horse and go further and that kind of thing so it's like when when it's a radically new product people don't know that they wantit because it's just not in their in their scope i think when they first started making tvs they did a nationwide survey i think this might have been like 46 or 48 it's like famous nationwide survey will you ever buy a tv no i was like 96 percent of respondents said no someone some crazy number like basically everyone's like would you buy a tv and maybe they put a price in there or something but it was famously almost everyone said they would not buy tv but they didn't know what they're talking aboutso the big game-changing stuff at the beginning is a company push kind of a thing most of the time but then changes to the product over time can be a lot more customer pull kind of a focus yeah change changes to the product over time can be incremental changes um then the customers can certainly tell you it's good to get customer feedback to say how can we improve the product and once they're using it they can say okay i like this thing about it i don't like this other thing and then we can improve the product over time customerfeedback after they they have the fundamental thing is is great when trying different things you've got to have some acceptance of failure as you're alluding to earlier failure must be an option if failure is not an option it's going to result in extremely conservative choices and you may not may get something even worse than lack of innovation things may go backwards so if what you really want is uh risk risk to you you want reward and punishment to to be proportionate to the actions that you seek so ifuh if what you're seeking is innovation then you should reward success and innovation um and only [Applause] there should be minor consequences for lack of minor consequences for for trying and failing should that should be minor um with significant rewards for trying and succeeding minor consequences for trying and not succeeding um and big and major negative consequences for not trying or maybe i just blank out the word doubt [Laughter] so uh you know i mean totally frank i doubted us too so i i thought we you know had maybewhen starting spacex maybe had a 10 chance of reaching orbit so so you know to those who who doubted us i was like well i think you're probably right you know um i mean there were times uh that i was told like uh because i was taking the money that i learned from from paypal and enrolling into to create spacex and tesla and they ended up spending it all it wasn't the intention but um and and and uh almost both companies went bankrupt frankly 2008 was a tough year you know it took us took us four attempts just to get to orbit withfalcon 1. um and uh so but a lot of times i was you know i people would tell me this joke like how do you make a small fortune in the rocket industry you start with a large one is the punch line and i was like okay i already heard that joke 12 000 times you know so so anyway um and it was it almost came true [Music] you know we just barely made it there that fourth launch of falcon one that's all the money we had for that fourth launch and then uh and that wasn't even enough to to save the company we also then hadto win the nasa cargo resupply contract um so that that came a little after you know a little bit later or towards the end of 2008 those are the two key things that that saved spacex otherwise we would have we would have you know not made it so so hey i think those those doubters were their probability assessment was correct um but fortunately uh vader smiled upon us and brought us to this day as you look back on your career in the space industry what has been the most surprising or unexpected challenge that you faced and along those lines ifyou were to go back in time and talk to your 20 year old self would you do anything differently go back in time to your 20 year old self i mean i think if i get it i think it would make far fewer mistakes obviously if i could go like here's a list of all the dumb things you're about to do please do not do them wouldn't we be a very long list and like here let me write it down or something you know um i mean it's hindsight's 2020. so it's hard to say um i mean a number of i've made so many foolish mistakes i had a lot count honestly um i mean some of these things i just wish i'd like that like that's a simple sort of mantra management by rhyming i mean it worked for homer okay um the management by rhyming is the thing i'm saying like if the if the schedule's long the design is wrong we've overcomplicated the design many times um and i think we should have just gone with a simpler design um with the acid test being how long will it take tofor this to fly and if it's going to take a long time don't do it do something else i think one thing that's important is if you have a choice of a lower evaluation with someone you really like or a higher evaluation with someone you have a question mark about take the lower valuation it's better to have a higher quality venture capitalist who you think is it would be great to work with than to um you know get a higher evaluation with someone where there's even a question mark really you know ithink that's that's important it's sort of like getting married you know i mean the way i tend to view problems is from a from a physics standpoint and i think i think physics is a good analytical framework um and one of the key things in in physics is to reason from first principles um this is contrary to the way most human reasoning takes place which is by analogy um reason for first principles just means that you you figure out what are the fundamental what are the fundamental truths or or things that are pretty people are prettysure are fundamental truths and and can you build up to a conclusion from from that uh or perform those principles and um and then certainly if you come up with some idea and it appears to violate one of those fundamental truths then you're probably wrong um or you should get a really big prize or something like that um so uh this may seem like i don't know it maybe it may seem sort of obvious when it's explained but it's actually not what people do reasoning my analogies is helpful because it's a shortcut yeah umand it's and it's mostly correct but but uh it tends to be most incorrect when you're dealing with new things because it's hard to analogize to something really new it doesn't really face the sphere threat of death due to the model 3 production ram essentially the company was bleeding money like crazy and and just if we didn't solve these problems in a very short period of time we would die and it was extremely difficult to solve them how close to death did you come we were within single digit weeks 22 hours a day likewhat how many hours working so seven days a week sleeping in the factory uh i worked everywhere from i worked in the paint shop general assembly body shop you ever worry about yourself imploding like just too much absolutely no one should put this many hours into work this is not good and people should not work this hard i'm not they should not do this this is very painful painful in what sense it's because my ears my brain and my heart and you started with a much smaller rocket that's often one and what's your goal at that point whenyou started with falcon 1 to get to the point where we had nine inches for falcon 9 was that your goal at that time when i started spacex i i only thought there was maybe a ten percent chance of getting felt than one two of us i did not at all think that this would happen uh so this is for sure a dream come true um uh but i i literally at the time i didn't know anything about rockets uh and i was you know i've been the chief engineer of spacex since day one and i don't hear anything about rockets whichis why the first three rockets failed right um and then so the first three falcon ones for space actually failures yes and then tell me about uh the fourth one so i had actually only had enough money for three three flights um so i had no more money that would manage to the team sort of rallied and we managed to put together enough spare parts to create a bit of a fourth launch and that both was successful and uh so what would happen if it wasn't successful oh we would just basically die so we would not be here right nowuh at this moment getting ready to launch crew dragon to the international system we've got a lot of work to do because we've got a lot of service centers and charge stations to construct so mostly it's like we're trying to build our service and charge infrastructure as fast as possible and i know this like some of the customers who have ordered a car they're not in the major cities so they're all unhappy with us because we are delaying delivery of their car and in fact i'm going to apologize to someof them personally to expand explain the reason we are delaying deliveries because it's we really want them to have a good experience but if they're too far from a service center and and the charging is not sorted out then they will not have a good experience so we're going to delay their cause just for a few months to make sure that they have a good experience i think it's also important to reason from first principles rather than by analogy so the normal way that we conduct our lives is we wewe reason by analogy um it's we're doing this because it's like something else that was done or it's like what um other people are doing me too type ideas yeah it's like yeah slight [Music] it's kind of mentally easier to reason by analogy rather than from first principles but my first principles is kind of a physics way of looking at the world and what that really means is you kind of boil things down to the most fundamental truths and and say okay what do we sure as true or sure as possibleis true and then reason up from there that takes a lot more mental energy um give me an example of that like what's one thing that you've you've done that on that you feels worked for you sure so um somebody could say um in fact people do uh that battery packs are really expensive and that's just the way they'll always be because that's the way they've been in the past um you're like well no that's that's pretty dumb you know because if if you apply that reasoning to anything new that thenyou wouldn't be able to ever get to that new thing right you are unusually fearless and willing to go in the face of other people telling you something is crazy and i know a lot of pretty crazy people you still stand out uh where does that come from or how do you think about making a decision when everyone tells you this is a crazy idea or where do you get the internal strength to do that well first of all i'd say i actually think i i think i feel feel fear quite strongly um so it's not as though i just have the absence of fear i've ifeel quite strongly um but there are just times when something is important enough you believe in it enough that you you do it in spite of fear so speaking of important things like people shouldn't think i i i i should if people should think well i feel fear about this and therefore i shouldn't do it um it's normal to be to feel fear like you'd have to definitely something mentally wrong if you didn't feel fair um so you just feel it and let the importance of it drive you to do it anyway yeah you know actually something thatcan be helpful is fatalism uh to some degree um if you just just accept the probabilities um then that diminishes fear uh so um starting spacex i thought the odds of success were less than ten percent um and i just accepted that actually probably i would just lose lose everything but that maybe would make some progress if we could just move the ball forward even if we died maybe some other company could pick up the baton and move and keep moving it forward so we still do some good um yeah same with tesla i thought theodds of a car company succeeding were extremely low this is widely regarded as one of the most robotics driven auto assembly lines on the planet elon part of the thing i heard about the model 3 is that there's too many robots that made it i agree you do you think so too that maybe you need more people in here working we do in some cases the robots actually slowed the production yes they did where this crazy complex uh network of conveyor belts and it was not working so we got rid of that whole thing thisis cool elon yeah realizing it needed an overhaul musk personally took over the model 3 production line at the beginning of april training for really extreme levels of precision uh more than any other vehicle in the world he says he has resorted to pulling all-nighters at the plant when things get really intense i don't have time to go home and shower and change so i sleep here i want to see where is that oh yeah um i mean it's pretty boring overall really um it's actually cold in here too yeah i like it cool so you have a you like itcold i sleep on the couch over there so you're just laying here on the couch yeah last time this year i actually slept literally on the floor because the couch was too narrow yeah i was gonna say and elon i have to say it's not even a comfortable couch either no it's terrible this is not a good couch musk feels like all the overtime is paying off and now he says the model 3 line is back on track and we're able to unlock some of the critical things that were holding us back from reaching 2000 cars a week but sincethen we've continued to do 2000 cars a week do you think that this is sustainable this pace is sustainable yeah i remember when you first told me that you were thinking about tunnels yeah what did i always tell you about that years ago okay it's like a long time ago i thought you were joking yeah it was i was joking but um it's not because of some epiphany that i had one day um driving down the 405.that's how it gets translated somehow i was talking about tunnels for years and years um for probably five years or four years at least whenever i'd give a talk and people would ask me about what opportunities you do you see in the world i'd say tunnels can someone please build tunnels so after four or five years of begging people to build tunnels and still no tunnels i was like okay i want to build a tunnel like maybe i'm missing something here um so um yes i was like basically talking people's ears or tunnels for for severalyears and then said well let's find out what it takes to build a tunnel and um yeah so i started digging a tunnel i wanted to start the tunnel uh from where i could see it from my office at spacex so i said well let's just carve off a part of the parking lot across the road so i can see if it's if anything's happening or not um and then we named our first boring machine uh godot because i kept waiting for it it never came um finally it did and and we got it going and um now we're making good progress i really take somethought to like how can i provide advice that would be most helpful and i'm not sure i've given enough thought to to that to give you the best possible answer but i think i think certainly being focused on something that you're confident will have high value to someone else and just being really rigorous in making that assessment [Music] because people are attenti natural human tendency is wishful thinking um so a challenge for entrepreneurs is to say well what's the difference between really believing in your ideals andsticking sticking to them versus pursuing some unrealistic dream that doesn't actually have merit and it's it's that is a that is a really difficult thing to to tell you can you tell the difference between those two things so you need to be sort of very rigorous um in your self self analysis um i think certainly extremely tenacious uh and um and then just work like hell i mean you just have to put in you know 80 hour 80 to 100 hour weeks every week because and then a lot of work that all those things improve the odds of success okayi mean if if other people are putting in 40-hour work weeks and you're putting in 100 hour work weeks then even if you're doing the same thing you know that in in one year you will achieve what they achieve you will achieve in four months what it takes them a year to achieve i certainly was quite um i was very very bookish i was reading all the time so i was either reading uh working my computer reading comics playing dungeon dragons that kind of thing i understand hitchhiker's guide to the galaxy that wonderful book by douglasadams that was a that was a key book for you what what was it about that book that that fired your imagination um yeah so uh i guess when i was in uh around 12 or 13 i had a company existential crisis and i was reading various books um on trying to figure out the meaning of life and well like what does it all mean because uh it starts seeming quite meaningless and then um uh my we happen to have like some some books by nietzsche and schopenhauer in the house which you should not read at age 14 is bad it's really negativeum so so uh but then i then i read the hitchhiker's guide the galaxy was like quite positive i think and um uh and it sort of highlighted the the an important point which is that a lot of times the question is harder than the answer and if you can properly phrase the question then the answer is the easy part and so uh the if to agree that we can better understand the universe then we better know what questions to ask and um then whatever the question is that most approx approximates what's the meaning of lifeyou know that that's that's the question we could ultimately get closer to understanding and so i thought well to agree that we can expand the scope and scale of consciousness and knowledge um human knowledge then that would be a good thing question that has been discussed over the past couple of days should we be considering one trips one-way only trips to mars uh what's the best uh approach to to colonize uh the planet is it uh well what's your view is that socially acceptable do you think people will signup to do it i think there's plenty of people that have signed up for a warranty trip to mars but maybe if i could we could have a show of hands who would consider such an option i see some not married or perhaps enough for a couple of missions so it's certainly suddenly beat up i mean i think it's sort of like is it a one-way mission and then you die or is it one-way mission and you get resupplied that's a big difference wait for the second option yeah exactly um but i mean i think it ends up being amoot point because you want to bring the spaceship back like these spaceships are expensive okay if they're hard to build you can't just leave them there so whether or not people want to come back or not is kind of like they can jump on if they want but they need the spaceship back thank you um i mean they're kind of weird like there was like a huge collection of spaceships on mars over time like it was like we should stay in the back and of course we should turn it back one of the most difficult choices i've everfaced in life was was in 2008 um and um i think it had like maybe 30 million dollars left or 30 or 41 left in 2008 i had two choices i could put it all into one company and then the other company would definitely die um or split it between the two companies and but if i split it between two companies then both might die and when you put your blood sweat and tears into creating something you're building something it's like a child and so it's like which one am i going to let one stop to death i can bring myself to do it so isplit the money between the two fortunately thank goodness they both came through i've heard people say listen he's out of the box thinker he's a businessman he's an entrepreneur but people that know you say i wouldn't say you would say i'm not really a business plan you're not a businessman no no what are you um i'm sure there's probably lots of analysts on wall street would agree that i'm not a businessman okay well what do you think you are um i like i'm an engineer an engineeryeah is it your dream to conquer the world and make the world a better place to what is your dream our technology's like magic you know i mean i think technology is the closest thing to magic that we have in the real world and so i think like engineering creative engineering is essentially technology development um and um i guess maybe it was like lord of the rings is my favorite book is it yeah that's really like what's the closest thing to being a wizard in the real world and that's like creating new technologies what was yourbiggest failure and how did that change i have to really think hard about that failure there's your answer well there's a ton of failures along the way that's for sure um like so as i said for spacex the first three launches failed and uh we were just barely able to scrape together enough parts and money to do the the fourth launch that fourth launch had failed we would have been dead so multiple failures along the way um i tried very hard to to get the right expertise in for for spacex i tried hard to to find a great chief engineer forthe rocket but the good chief engineers wouldn't join and the bad ones well there was no point in hiring them so i ended up being chief engineer of the rocket so if i could have found somebody better than we would have maybe had less than three failures what motivations do people need to harness to try to make change as opposed to just reading about change um and that's not supposed to be an easy question oh it's this well like i said i think if if you study engineering and you figure out how to design new thingsthen it's relatively easy to start a company you just need to get a few like-minded people with you and and then focus on creating a prototype for compelling prototype as soon as possible um and then that you know there's a there's a strong venture uh capital industry in this country that will give you funding to take things to the next level um and that that's all there is to it um and you might if you know tried a few times you might may or may not succeed but um i think sometimes people fear fear starting a company toomuch um you know they have to say really what's worse that could go wrong you're not going to stop to death you're not going to die of exposure what's the worst that could go wrong how do you come with this idea actually sometimes they're pushing the human limit you are always pushing the human limit why well i i when i i think about what what technology solution is necessary in order to achieve the particular goal and then try to make as much progress in that direction being a multi-planetspecies and being out there among the stars is important for the long-term survival of humanity and that's one reason kind of like life insurance for life collectively life as we know it but then the part that i find personally most motivating is that it creates a sense of adventure and it makes people excited about the future and if you consider two futures one where we are forever confined to earth until eventually something terrible happens or another future where we are out there on many planets maybe even going beyondthe solar system i think that second version is incredibly exciting and inspiring and there need to be reasons to get up in the morning you know life can't just be about solving problems otherwise what's the point there's got to be things that people find inspiring and make life worth living there's a friend of mine who's got a great saying about creating a company which is creating trying to build a company and have it succeed is like eating glass and staring into the abyss so i mean what tends to happen is it'ssort of quite exciting for the first several months of starting a company and then then reality sets in things don't go as well as planned customers aren't signing up the technology or the product isn't working as well as you thought and and then that can sometimes be compounded by a recession and it can be very very painful for several years um so i think frankly starting a company i would advise people to have a high pain tolerance when did it occur to you that zip2 might be a success well i mean when we first started out ithink our ambitions were really quite quite low um it was really to make enough money to pay the rent yeah we we got a visa give us money that was yay we thought it was all over then yeah it was pretty crazy i mean when we started out at 95 we literally at the beginning we had one computer which um would be the web server during the day and and then at night i'd program on it and we'd sleep in the office yeah we couldn't afford to to yeah an apartment it was cheaper to rent the office than to rent an apartment so we just rentedthe office and stepped in the office and showered at the end of the ymca and for me the worst part was eating a jack-in-the-box three times yeah man this is like i'll see it's really difficult to get food at palo alto after like 10 p.m um it's like jack-in-the-box and a few other options we rotated through the jack-in-the-box menu through the end of 95 where that's essentially just sleeping in the office and charging the ymca and then um and around the end of 95 is when netscape went public andand then whether or not somebody knew what the internet was they knew that you could make money on the internet somehow or even if it's only on the greater pool theory so when we went and talked to venture capitalists in early 96 there was a much greater interest in what we were doing um in fact the round closed in like maybe a week or something it's crazy yeah we went from sleeping in the office to people throwing i mean again this is a financial crowd so you guys see these numbers every day but for us to herewe'll give you three million dollars yeah sounded extremely we thought they were crazy like why would they do that it was literally like these people are insane they obviously do not realize we're sleeping in the office in fact when they when they did fund us they realized that we were illegal immigrants well yes we were i've seen it was a great area yeah yes we were i was there we were illegal immigrants we were sleeping in the office we didn't have a car we had one car with the wheel kept falling off but well actually yeahthe the wheel didn't actually fall off the car oh yes exactly um and then and the venture capitalists actually bought us cars yeah well like it gave us 40 grand give us 40 grand to go buy cars which was at the time was more money than we've ever seen yeah you need a team around you to deliver a lot of idea how do you choose your team based on what well i suppose honestly that it tends to be gut feel more than anything else so when i interview somebody my interview question is always the same it's justi said tell me the story of your life and the decisions that you made along the way and why you made them and then um and and also tell me about some of the most difficult problems you worked on and how you solved them and that that question i think is very important because the people that really solve the problem they know exactly how they solved it they know the little details and the people that pretended to solve the problem they can maybe go one level and then they get stuck the president's first space policydirective to me was go to the moon and the word in there is sustainable tell me reusability is fundamental um the the fully reusable vehicle uh will cost uh a hundred times less per flight than an expandable vehicle it kind of makes sense think of uh of any other motor transport it could be like uh aircraft or cars bicycles horses every other mode of transport boats they're all reusable the only the weird one that is reusable is space that's right so um you can imagine how expensive it would be if every time you flew in a jet that youhad to get a new jet right as opposed to refuel the jet that would be insanely expensive to fly a jedi a single use there wouldn't be anybody flying no exactly i'd be like a few research flights at uh at an extreme expense and that's that's all the flying that would occur i actually don't care at all about money at all but i do care about us becoming a space-bringing civilization yeah and i do know that uh if we don't uh achieve full and rapid reusability it will not happen and so that's why that's the onlyreason i actually want money at all what i really want to see is you know coming to face on the moon permanently uh occupied human base on the moon and us building a city on mars that's like i can see the beginning of that before i die i'll die happy in creating these companies that we thought that we would be successful i thought that the most likely outcome was failure but but it was still worth doing even though the odds of success were low in fact even for for support spacex the originally what i started doing was notcreating a rocket company but but actually was going to do a small mission to mars which was just a philanthropic mission where you would send a small greenhouse with seeds and dehydrated gel in the wood upon landing hydrate the gel and you'd have this cool picture of green plants on a red background and the public tends to respond to precedence and superlatives so this will be the first life on mars furthest that life's ever traveled and you'd have this great money shot of green plants on the right backgroundso um yeah i thought that would that would get people's attention so um but but the expectation for that was was no return so i thought we wouldn't get any uh you know we'll just spend the money on that and it wouldn't wouldn't happen you want to do some things that were of benefit to humanity why why did you think that well because not everyone does yeah no i i guess it was um i it's sort of a existential crisis of like what does it all mean and what's the meaning you know what's the meaningof life and uh is this three a.m over a beer or this was well more seriously probably goes back to high school i guess uh um i don't want to give a laboriously long answer but uh i was uh i yeah i had sort of a dark childhood it wasn't good um probably partially brought on by by reading some of the philosophers like do don't ever read schopenhauer nietzsche if you're 14.it's it's not good yeah or ein rand either too yeah yeah so um i was just trying to find figure out what you know what does it all mean and um actually uh when i read the hitchhiker's guide to the galaxy which i think is a great working philosophy um that sort of highlighted the point that uh very often the issue is understanding what questions to ask and if you can properly frame the question then the answer is the easy part um so i thought uh things that uh expand the scope and scale of human consciousness um and allow us to betterask questions and you know and and and achieve greater enlightenment those are good things and so that's sort of what what can we do that's going to most likely lead to that outcome back in 95 there weren't very many people on the internet and certainly nobody was making any money at all most people thought the internet was going to be a fad a year ago musk sold his software company zip2 which enabled newspapers to publish online for 400 million dollars cash receiving cash is cash i mean those are just a large number of ben franklin's sothis is an atm and what we're going to do is transform the traditional banking industry i do not fit the picture of a banker x.com this is julie raising 50 million is a matter of making a series of phone calls and the money is there i've sunk the great majority of of my net worth into x.com which is the new banking and mutual funds company on the internet that i've started exactly x.com i think x.com could absolutely be a multi-billion dollar bonanza because if you look at the industry that x is pursuing it's the biggest sector of the world economy and what you've got going on with the internet is it's basically like an earthquake where the epicenter is silicon valley and it's it's shaking up the whole world elon you've been compared to henry ford richard branson you know steve jobs who do you compare yourself to um and idon't really compare myself to anyone um i mean it's not um i mean there's some people that i admire from history that i think are you know i think are great um so certainly many of the scientists and engineers and literary figures and so forth and uh like i'm a big big fan of ben franklin you know he was a scientist and sort of thinker and yeah i mean he was kind of guy who did did what needed to be done you know so i like guys like that i right i wouldn't say i compare myself in any way but i certainly admire them now i'vegot a special bonus clip that i think you're gonna enjoy but before that it's time for the question of the day i wanna know what was your single biggest takeaway from this video and what is your specific plan of action for the next week when you just watch a video and get motivated by it you have a 35 chance of following through but when you get motivated and then create a specific plan of action you have a 91 chance of following through that's what we do here believe nation we get motivated but thenwe do something about it and when you commit to other people you increase your chances even further of following through so what was your biggest takeaway from this video and then what is your plan of action around for this week put it down in the comments below because i want to celebrate you well i think part of the problem the reason people aren't as excited about space is that we haven't been pushing the frontier as much and so you can only you can only watch the same movie so many times and it before it gets a little boringum and you know in in the 60s and early 70s we're really pushing the frontier of human space flight and and obviously that those landing on the moon is regarded as one of the greatest achievements of humanity of arguability of life itself and and even though only a handful of people went to the moon vicariously we all went there well at least i wasn't alive at the time so but retrospectively and you know and it was it was just one of those really inspiring things that i think made everyone glad to be a youknow human you know it's like the things that we where we don't they're bad things human ideas and they're good things and and that's one of the good things um and i i do think it's important that that we have these inspiring things that uh uh you make you glad to get up in the morning and um and that that's uh and and glad to be a member of human race um and and and we need to we need to push that that frontier um so um and and i think uh the the great goal we should be trying to pursue istrying to make life huma like make life multi-planetary so to to establish a self-sustaining and growing uh civilization on another planet uh mars being the only realistic possibility um and uh and i think that would just be one of the greatest things humanity could ever try to do if you want to know the five pieces of life-changing advice from bill gates check out the video right there next to me i think you'll enjoy it continue to believe and i'll see you there when you have a business that's important as thiswith this many competitors you're going to have people saying some nasty things the thing that was scary to me wasn't quitting and starting the company it was when i started hiring my friends.