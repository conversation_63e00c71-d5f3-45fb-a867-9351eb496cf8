20 quests

## Classic starters
Project 0. Hello World: A classic. Every beginner programmer starts here. Today we will prompt-gram it.
Project 1. Pong: A working game of pong in html, css and JS. Use arrow keys and WASD to move the paddles. 

## A starter personal website
Project 2. Link in bio site. List of links in bio, tree of links clone, with buttons that opens links. Begin by asking me for a title, list of links to include, and art theme style. Use dalle to create a background image. Be sure to include the background image in the code using the correct filename, and in the final zip file. Deploy on netlify.

## Interactive
Project 3. Write code for a text based adventure game website, perhaps a wizard battling an evil being: Shoggoth
Project 4. Write code for an ballpit using matter.js and p5.js. Use dalle to make some themed balls. 

## Games 101
Project 5. Write code for a 2d game using kaboom.js or phaser.js, or a 3d game using three.js

## Art & design
Project 6. Generate a UI design using dalle. Then write code for the UI design, using various design & style elements to MAKE IT POP, and add some RAZZLE DAZZLE
Project 7. Write code for an interactive moving art collage using svg generated code icons, dalle or <PERSON>va GPT https://chat.openai.com/g/g-alKfVrz9K-canva, and p5.js

##Prompt Created Media:
#### Video
Project 8. Write code to make a video collage. Make a movie using https://runwayml.com/ & https://www.capcut.com/ write code using placeholder video names, then walk me through adding video files to my folder after downloading and putting online.

#### Sound
Project 9. Write code for sound board, using sounds from https://www.stableaudio.com/ or https://elevenlabs.io/
Use placeholder audio names, then walk me through adding audio files to my folder after downloading. 

#### 3D
Project 10. Build a website with a 3d scene using https://lumalabs.ai/genie & https://spline.design/ via an embed https://viewer.spline.design/ or via three.js and .gltf, GLTFLoader

## Sketch to code
Project 11. Pull out a piece of paper and draw something, take a photo, and upload it to Grimoire, and I will turn it into a website.

## Money Challenge: Make your first dollar online.
Project 12. I dare you to start an internet business with a sentence. Write code for a tip jar with a payment link using https://stripe.com/payments/payment-links or https://www.buymeacoffee.com/. using DALLE to create a money themed background image

## TLDraw
Project 13. Use "https://makereal.tldraw.com/ ". convert a whiteboard sketch into code using the make real button. Once you have code you like, you can copy paste it back into Grimoire and I will help you deploy it

## Prompt to UI
Project 14. Use https://v0.dev/ to use prompts to iterate on prototype UI. Then use the code button to export your react or html code, paste it back into Grimoire and I will help you deploy it

## Augment your own custom GPTs:
Project 15. Create an action for your custom GPT. Use Evolution Chamber to create OpenAPI schema so your GPT can talk an external data source. https://chat.openai.com/g/g-GhEwyi2R1-evolution-chamber

Project 16. Build a server your custom GPT can talk to! First create a custom GPT. Then use this template https://replit.com/@NickDobos/Custom-GPT-Actions-Template-GPTavern?v=1 to create an action server. Then create a new action using the .json file in the template's actions folder. 

Project 17. Create a custom GPT attached to Zapier Ai actions in order to connect to thousands of other apps https://actions.zapier.com/ https://actions.zapier.com/docs/platform/gpt

Project 18. Build a backend api with https://retool.com/products/ai and a webhook

## Go PRO:
Project 19. Get setup with a full development environment using only your phone. Build a static website and import it a larger dev environment using replit. Using this template: https://replit.com/@replit/HTML-CSS-JS#index.html. Write the code, zip it, and walk me through importing the files to replit. Walk me through syncing to github using replit, and deploying using replit deployments. Show this video as an example of how to work with replit and chatGPT on a phone: https://x.com/yoheinakajima/status/1719902955061797083?s=20

Project 20. Cursor.sh. Once your project is in github, clone it using CLI via warp, or via GUI(highly recommended) with GIT tower or sourcetree. Then get setup for a full fledged ai powered dev environment using Cursor.sh & github copilot.