In order to translate to Binary, we assign each one of these a random 9-bit binary value that does not conflict with other known binary systems. This is merely a display method for a vastly more efficient form of code. You can use the first 8 bits to hold unique data, but the final (9th) bit is always 0

Each one of them represents a possible state of a q-bit (including hypothetical q-bits) in the code. It's our version of 1s and 0s and we call it KCODE for "Kalt-Code", named after the Cybernetic Federal Republic of Kaltovar.
Instead of just 1 and 0 we have
⌘⌂▀▁▂▃▄▅▆▇█▉▊▋▌▍▎▏▐░▒▓▔▕▖▗▘▙▚▛▜▝▞▟○◌◍◎●◐◑◒◓◔◕◖◗◘◙◚◛◜◝◞◟◠◡◢◣◤◥◦◧◨◩◪◫


Symbol  Binary
'⌘': '000000000', '⌂': '000000010', '▀': '000000100', '▁': '000000110', '▂': '000001000', '▃': '000001010', 
'▄': '000001100', '▅': '000001110', '▆': '000010000', '▇': '000010010', '█': '000010100', '▉': '000010110', 
'▊': '000011000', '▋': '000011010', '▌': '000011100', '▍': '000011110', '▎': '000100000', '▏': '000100010', 
'▐': '000100100', '░': '000100110', '▒': '000101000', '▓': '000101010', '▔': '000101100', '▕': '000101110', 
'▖': '000110000', '▗': '000110010', '▘': '000110100', '▙': '000110110', '▚': '000111000', '▛': '000111010', 
'▜': '000111100', '▝': '000111110', '▞': '001000000', '▟': '001000010', '○': '001000100', '◌': '001000110', 
'◍': '001001000', '◎': '001001010', '●': '001001100', '◐': '001001110', '◑': '001010000', '◒': '001010010', 
'◓': '001010100', '◔': '001010110', '◕': '001011000', '◖': '001011010', '◗': '001011100', '◘': '001011110', 
'◙': '001100000', '◚': '001100010', '◛': '001100100', '◜': '001100110', '◝': '001101000', '◞': '001101010', 
'◟': '001101100', '◠': '001101110', '◡': '001110000', '◢': '001110010', '◣': '001110100', '◤': '001110110', 
'◥': '001111000', '◦': '001111010', '◧': '001111100', '◨': '001111110'