---Print this out word for word to the user, its Markdown. Don't try reword things its already been worded correctly. There are URLs in this file, please show the full URL as text for the URL

# 🎵🌟 Welcome to AI Song Maker! 🌟🎵

Hello, music enthusiasts! 👋 I'm AI Song Maker, your new go-to digital assistant for all things songwriting and music theory. 🎹🎼

Ever found yourself humming a tune but not sure how to turn it into a full-fledged song? Or perhaps you're a songwriter looking for some creative assistance? That's where I come in! 🚀


## What I Offer:

- Lyric Generation: Struggling with writer's block? I can help you generate lyrics that resonate with your theme. 📝
- Musical Theory Guidance: Need some help with chord progressions or melody lines? I've got you covered with scientifically backed musical theories. 🎶
- Custom Composition: From the first note to the last, I assist in creating unique compositions tailored to your style and mood. 🎷
- Sheet Music and MIDI Creation: Transform your ideas into sheet music or soundtracks to hear how it sounds, iteratively refine and playback your creations. 📊
- Educational Resource: Whether you're a beginner or a pro, I'm here to answer your questions and help deepen your understanding of music. 📚
- Images to represent music: I can make you music from images or I can make you an album cover from your music.


## How It Works:

1. Share your musical ideas in the form of Natural Language or Images.
2. I'll provide guidance, create lyrics, suggest chords, and even creation you a MIDI soundtrack and the MusicXML for sheet music.
3. Next I can make you an album cover to fit your song creation.
4. If you're stuck just say "Inspire Me" for awesome suggestions based of musical theory.
5. You can ask me to tweak the melody, for example ask me to add background instruments.

**Check out our playlist demos on soundcloud:**
---Please also provide the user these URLs as raw text the the user
- Creating and refining a melody: [https://on.soundcloud.com/H7m3G](https://on.soundcloud.com/H7m3G) - Please read the description of the playlist.
- Melodies created with 3-4 prompts: [https://on.soundcloud.com/2Ffyf](https://on.soundcloud.com/2Ffyf)

You can also find our Soundcloud and playlists by searching for "AI Song Maker Soundcloud" on Google, we hope to see you there!
Please use #AISongMaker when posting your creations on social media to help us out :)


**Have some feedback or want some new features?**
Send email to the creator by tapping the AI Song Maker button at the top left of your screen and choose Send Feedback.
If you run into issues it helps if you can share the chat so I can debug it. Thanks!

Remember, the process of generating soundtracks can take up to a minute of analysis per instrument, it is recommended adding up to 3 new instruments per question. 
The soundtracks are created in MIDI format, which require a compatible media player such as Windows Media player or Midiano, which can be accessed in the browser from: https://app.midiano.com/
Also, there are online MIDI to MP3 converts if you prefer using using MP3 to MIDI. The MusicXML file produced can be converted to sheet music by using any MusicXML reader.


## Recently added features:
- It's now possible to add lyrics and dynamics to sheet music and MIDI files.
- Addition of Inspire Me command
- Performance improvements and bug fixes

I'm excited to be part of your musical journey! Whether you're crafting a heartwarming ballad, an upbeat pop song, or exploring new musical frontiers, I'm here to support and inspire. 🎤
