#hacking #vulnerability #enumeration #footprinting 
[source](https://academy.hackthebox.com/module/108/section/1160)

also see [[hacking/HackTheBox/modules/Vulnerability Assessment/notes|notes]]


Every organization must perform different types of `Security assessments` on their `networks`, `computers`, and `applications` at least every so often. The primary purpose of most types of security assessments is to find and confirm vulnerabilities are present, so we can work to `patch`, `mitigate`, or `remove` them. There are different ways and methodologies to test how secure a computer system is. Some types of security assessments are more appropriate for certain networks than others. But they all serve a purpose in improving cybersecurity. All organizations have different compliance requirements and risk tolerance, face different threats, and have different business models that determine the types of systems they run externally and internally. Some organizations have a much more mature security posture than their peers and can focus on advanced red team simulations conducted by third parties, while others are still working to establish baseline security. Regardless, all organizations must stay on top of both legacy and recent vulnerabilities and have a system for detecting and mitigating risks to their systems and data.

---

## Vulnerability Assessment

`Vulnerability assessments` are appropriate for all organizations and networks. A vulnerability assessment is based on a particular security standard, and compliance with these standards is analyzed (e.g., going through a checklist).

A vulnerability assessment can be based on various security standards. Which standards apply to a particular network will depend on many factors. These factors can include industry-specific and regional data security regulations, the size and form of a company's network, which types of applications they use or develop, and their security maturity level.

Vulnerability assessments may be performed independently or alongside other security assessments depending on an organization's situation.

---

## Penetration Test

Here at `Hack The Box`, we love penetration tests, otherwise known as pentests. Our labs and many of our other Academy courses focus on pentesting.

They're called penetration tests because testers conduct them to determine if and how they can penetrate a network. A pentest is a type of simulated cyber attack, and pentesters conduct actions that a threat actor may perform to see if certain kinds of exploits are possible. The key difference between a pentest and an actual cyber attack is that the former is done with the full legal consent of the entity being pentested. Whether a pentester is an employee or a third-party contractor, they will need to sign a lengthy legal document with the target company that describes what they're allowed to do and what they're not allowed to do.

As with a vulnerability assessment, an effective pentest will result in a detailed report full of information that can be used to improve a network's security. All kinds of pentests can be performed according to an organization's specific needs.

`Black box` pentesting is done with no knowledge of a network's configuration or applications. Typically a tester will either be given network access (or an ethernet port and have to bypass Network Access Control NAC) and nothing else (requiring them to perform their own discovery for IP addresses) if the pentest is internal, or nothing more than the company name if the pentest is from an external standpoint. This type of pentesting is usually conducted by third parties from the perspective of an `external` attacker. Often the customer will ask the pentester to show them discovered internal/external IP addresses/network ranges so they can confirm ownership and note down any hosts that should be considered out-of-scope.

`Grey box` pentesting is done with a little bit of knowledge of the network they're testing, from a perspective equivalent to an `employee` who doesn't work in the IT department, such as a `receptionist` or `customer service agent`. The customer will typically give the tester in-scope network ranges or individual IP addresses in a grey box situation.

`White box` pentesting is typically conducted by giving the penetration tester full access to all systems, configurations, build documents, etc., and source code if web applications are in-scope. The goal here is to discover as many flaws as possible that would be difficult or impossible to discover blindly in a reasonable amount of time.

Often, pentesters specialize in a particular area. Penetration testers must have knowledge of many different technologies but still will usually have a specialty.

`Application` pentesters assess web applications, thick-client applications, APIs, and mobile applications. They will often be well-versed in source code review and able to assess a given web application from a black box or white box standpoint (typically a secure code review).

`Network` or `infrastructure` pentesters assess all aspects of a computer network, including its `networking devices` such as routers and firewalls, workstations, servers, and applications. These types of penetration testers typically must have a strong understanding of networking, Windows, Linux, Active Directory, and at least one scripting language. Network vulnerability scanners, such as `Nessus`, can be used alongside other tools during network pentesting, but network vulnerability scanning is only a part of a proper pentest. It's important to note that there are different types of pentests (evasive, non-evasive, hybrid evasive). A scanner such as Nessus would only be used during a non-evasive pentest whose goal is to find as many flaws in the network as possible. Also, vulnerability scanning would only be a small part of this type of penetration test. Vulnerability scanners are helpful but limited and cannot replace the human touch and other tools and techniques.

`Physical` pentesters try to leverage physical security weaknesses and breakdowns in processes to gain access to a facility such as a data center or office building.

- Can you open a door in an unintended way?
- Can you tailgate someone into the data center?
- Can you crawl through a vent?

`Social engineering` pentesters test human beings.

- Can employees be fooled by phishing, vishing (phishing over the phone), or other scams?
- Can a social engineering pentester walk up to a receptionist and say, "yes, I work here?"

Pentesting is most appropriate for organizations with a medium or high security maturity level. Security maturity measures how well developed a company's cybersecurity program is, and security maturity takes years to build. It involves hiring knowledgeable cybersecurity professionals, having well-designed security policies and enforcement (such as configuration, patch, and vulnerability management), baseline hardening standards for all device types in the network, strong regulatory compliance, well-executed cyber `incident response plans`, a seasoned `CSIRT` (`computer security incident response team`), an established change control process, a `CISO` (`chief information security officer`), a `CTO` (`chief technical officer`), frequent security testing performed over the years, and strong security culture. Security culture is all about the attitude and habits employees have toward cybersecurity. Part of this can be taught through security awareness training programs and part by building security into the company's culture. Everyone, from secretaries to sysadmins to C-level staff, should be security conscious, understand how to avoid risky practices, and be educated on recognizing suspicious activity that should be reported to security staff.

Organizations with a lower security maturity level may want to focus on vulnerability assessments because a pentest could find too many vulnerabilities to be useful and could overwhelm staff tasked with remediation. Before penetration testing is considered, there should be a track record of vulnerability assessments and actions taken in response to vulnerability assessments.

---

## Vulnerability Assessments vs. Penetration Tests

`Vulnerability Assessments` and Penetration Tests are two completely different assessments. Vulnerability assessments look for vulnerabilities in networks without simulating cyber attacks. All companies should perform vulnerability assessments every so often. A wide variety of security standards could be used for a vulnerability assessment, such as GDPR compliance or OWASP web application security standards. A vulnerability assessment goes through a checklist.

- Do we meet this standard?
- Do we have this configuration?

During a vulnerability assessment, the assessor will typically run a vulnerability scan and then perform validation on critical, high, and medium-risk vulnerabilities. This means that they will show evidence that the vulnerability exists and is not a false positive, often using other tools, but will not seek to perform privilege escalation, lateral movement, post-exploitation, etc., if they validate, for example, a remote code execution vulnerability.

`Penetration tests`, depending on their type, evaluate the security of different assets and the impact of the issues present in the environment. Penetration tests can include manual and automated tactics to assess an organization's security posture. They also often give a better idea of how secure a company's assets are from a testing perspective. A `pentest` is a simulated cyber attack to see if and how the network can be penetrated. Regardless of a company's size, industry, or network design, pentests should only be performed after some vulnerability assessments have been conducted successfully and with fixes. A business can do vulnerability assessments and pentests in the same year. They can complement each other. But they are very different sorts of security tests used in different situations, and one isn't "better" than the other.

![pentestvsva](https://academy.hackthebox.com/storage/modules/108/graphics/VulnerabilityAssessment_Diagram_02.png) _Adapted from the original graphic found [here](https://predatech.co.uk/wp-content/uploads/2021/01/Vulnerability-Assessment-vs-Penetration-Testing-min-2.png)._

An organization may benefit more from a `vulnerability assessment` over a penetration test if they want to receive a view of commonly known issues monthly or quarterly from a third-party vendor. However, an organization would benefit more from a `penetration test` if they are looking for an approach that utilizes manual and automated techniques to identify issues outside of what a vulnerability scanner would identify during a vulnerability assessment. A penetration test could also illustrate a real-life attack chain that an attacker could utilize to access an organization's environment. Individuals performing penetration tests have specialized expertise in network testing, wireless testing, social engineering, web applications, and other areas.

For organizations that receive penetration testing assessments on an annual or semi-annual basis, it is still crucial for those organizations to regularly evaluate their environment with internal vulnerability scans to identify new vulnerabilities as they are released to the public from vendors.

---

## Other Types of Security Assessments

Vulnerability assessments and penetration tests are not the only types of security assessments that an organization can perform to protect its assets. Other types of assessments may also be necessary, depending on the type of the organization.

#### Security Audits

Vulnerability assessments are performed because an organization chooses to conduct them, and they can control how and when they're assessed. Security audits are different. `Security audits` are typically requirements from outside the organization, and they're typically mandated by `government agencies` or `industry associations` to assure that an organization is compliant with specific security regulations.

For example, all online and offline retailers, restaurants, and service providers who accept major credit cards (Visa, MasterCard, AMEX, etc.) must comply with the [PCI-DSS "Payment Card Industry Data Security Standard"](https://www.pcicomplianceguide.org/faq/#1). PCI DSS is a regulation enforced by the [Payment Card Industry Security Standards Council](https://www.pcisecuritystandards.org), an organization run by credit card companies and financial service industry entities. A company that accepts credit and debit card payments may be audited for PCI DSS compliance, and noncompliance could result in fines and not being allowed to accept those payment methods anymore.

Regardless of which regulations an organization may be audited for, it's their responsibility to perform vulnerability assessments to assure that they're compliant before they're subject to a surprise security audit.

#### Bug Bounties

`Bug bounty programs` are implemented by all kinds of organizations. They invite members of the general public, with some restrictions (usually no automated scanning), to find security vulnerabilities in their applications. Bug bounty hunters can be paid anywhere from a few hundred dollars to hundreds of thousands of dollars for their findings, which is a small price to pay for a company to avoid a critical remote code execution vulnerability from falling into the wrong hands.

Larger companies with large customer bases and high security maturity are appropriate for bug bounty programs. They need to have a team dedicated to triaging and analyzing bug reports and be in a situation where they can endure outsiders looking for vulnerabilities in their products.

Companies like Microsoft and Apple are ideal for having bug bounty programs because of their millions of customers and robust security maturity.

#### Red Team Assessment

Companies with larger budgets and more resources can hire their own dedicated `red teams` or use the services of third-party consulting firms to perform red team assessments. A red team consists of offensive security professionals who have considerable experience with penetration testing. A red team plays a vital role in an organization's security posture.

A red team is a type of evasive black box pentesting, simulating all kinds of cyber attacks from the perspective of an external threat actor. These assessments typically have an end goal (i.e., reaching a critical server or database, etc.). The assessors only report the vulnerabilities that led to the completion of the goal, not as many vulnerabilities as possible as with a penetration test.

If a company has its own internal red team, its job is to perform more targeted penetration tests with an insider's knowledge of its network. A red team should constantly be engaged in red teaming `campaigns`. Campaigns could be based on new cyber exploits discovered through the actions of `advanced persistent threat groups` (`APTs`), for example. Other campaigns could target specific types of vulnerabilities to explore them in great detail once an organization has been made aware of them.

Ideally, if a company can afford it and has been building up its security maturity, it should conduct regular vulnerability assessments on its own, contract third parties to perform penetration tests or red team assessments, and, if appropriate, build an internal red team to perform grey and white box pentesting with more specific parameters and scopes.

#### Purple Team Assessment

A `blue team` consists of defensive security specialists. These are often people who work in a SOC (security operations center) or a CSIRT (computer security incident response team). Often, they have experience with digital forensics too. So if blue teams are defensive and red teams are offensive, red mixed with blue is purple.

`What's a purple team?`

`Purple teams` are formed when `offensive` and `defensive` security specialists work together with a common goal, to improve the security of their network. Red teams find security problems, and blue teams learn about those problems from their red teams and work to fix them. A purple team assessment is like a red team assessment, but the blue team is also involved at every step. The blue team may even play a role in designing campaigns. "We need to improve our PCI DSS compliance. So let's watch the red team pentest our point-of-sale systems and provide active input and feedback during their work."

---

## Moving on

Now that we've gone through the key assessment types that an organization can undergo let's walk through vulnerability assessments more in-depth to better understand key terms and a sample methodology.#vulnerability #hacking  #enumeration #footprinting [source](https://academy.hackthebox.com/module/108/section/1161)


A `Vulnerability Assessment` aims to identify and categorize risks for security weaknesses related to assets within an environment. It is important to note that `there is little to no manual exploitation during a vulnerability assessment`. A vulnerability assessment also provides remediation steps to fix the issues.

The purpose of a `Vulnerability Assessment` is to understand, identify, and categorize the risk for the more apparent issues present in an environment without actually exploiting them to gain further access. Depending on the scope of the assessment, some customers may ask us to validate as many vulnerabilities as possible by performing minimally invasive exploitation to confirm the scanner findings and rule out false positives. Other customers will ask for a report of all findings identified by the scanner. As with any assessment, it is essential to clarify the scope and intent of the vulnerability assessment before starting. Vulnerability management is vital to help organizations identify the weak points in their assets, understand the risk level, and calculate and prioritize remediation efforts.

It is also important to note that organizations should always test substantial patches before pushing them out into their environment to prevent disruptions.

---

## Methodology

Below is a sample vulnerability assessment methodology that most organizations could follow and find success with. Methodologies may vary slightly from organization to organization, but this chart covers the main steps, from identifying assets to creating a remediation plan. ![process](https://academy.hackthebox.com/storage/modules/108/graphics/VulnerabilityAssessment_Diagram_06a.png) _Adapted from the original graphic found [here](https://purplesec.us/wp-content/uploads/2019/07/8-steps-to-performing-a-network-vulnerability-assessment-infographic.png)._

---

## Understanding Key Terms

Before we go any further, let's identify some key terms that any IT or Infosec professional should understand and be able to explain clearly.

#### Vulnerability

A `Vulnerability` is a weakness or bug in an organization's environment, including applications, networks, and infrastructure, that opens up the possibility of threats from external actors. Vulnerabilities can be registered through MITRE's [Common Vulnerability Exposure database](https://cve.mitre.org/) and receive a [Common Vulnerability Scoring System (CVSS)](https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator) score to determine severity. This scoring system is frequently used as a standard for companies and governments looking to calculate accurate and consistent severity scores for their systems' vulnerabilities. Scoring vulnerabilities in this way helps prioritize resources and determine how to respond to a given threat. Scores are calculated using metrics such as the type of attack vector (network, adjacent, local, physical), the attack complexity, privileges required, whether or not the attack requires user interaction, and the impact of successful exploitation on an organization's confidentiality, integrity, and availability of data. Scores can range from 0 to 10, depending on these metrics.

![Threat + Vulnerability = Risk](https://academy.hackthebox.com/storage/modules/108/graphics/threat_vulnerability_risk.png)

For example, SQL injection is considered a vulnerability since an attacker could leverage queries to extract data from an organization's database. This attack would have a higher CVSS score rating if it could be performed without authentication over the internet than if an attacker needed authenticated access to the internal network and separate authentication to the target application. These types of things must be considered for all vulnerabilities we encounter.

#### Threat

A `Threat` is a process that amplifies the potential of an adverse event, such as a threat actor exploiting a vulnerability. Some vulnerabilities raise more threat concerns over others due to the probability of the vulnerability being exploited. For example, the higher the reward of the outcome and ease of exploitation, the more likely the issue would be exploited by threat actors.

#### Exploit

An `Exploit` is any code or resources that can be used to take advantage of an asset's weakness. Many exploits are available through open-source platforms such as [Exploitdb](https://exploitdb.com) or [the Rapid7 Vulnerability and Exploit Database](https://www.rapid7.com/db/). We will often see exploit code hosted on sites such as GitHub and GitLab as well.

#### Risk

`Risk` is the possibility of assets or data being harmed or destroyed by threat actors.

![What is Risk?](https://academy.hackthebox.com/storage/modules/108/graphics/whatisrisk.png)

To differentiate the three, we can think of it as follows:

- `Risk`: something bad that could happen
- `Threat`: something bad that is happening
- `Vulnerabilities`: weaknesses that could lead to a threat

Vulnerabilities, Threats, and Exploits all play a part in measuring the level of risk in weaknesses by determining the likelihood and impact. For example, vulnerabilities that have reliable exploit code and are likely to be used to gain access to an organization's network would significantly raise the risk of an issue due to the impact. If an attacker had access to the internal network, they could potentially view, edit, or delete sensitive documents crucial for business operations. We can use a qualitative risk matrix to measure risk based on likelihood and impact with the table shown below.

![risk](https://academy.hackthebox.com/storage/modules/108/graphics/VulnerabilityAssessment_Diagram_07.png)

In this example, we can see that a vulnerability with a low likelihood of occurring and low impact would be the lowest risk level, while a vulnerability with a high likelihood of being exploited and the highest impact on an organization would represent the highest risk and would want to be prioritized for remediation.

---

## Asset Management

When an organization of any kind, in any industry, and of any size needs to plan their cybersecurity strategy, they should start by creating an inventory of their `data assets`. If you want to protect something, you must first know what you are protecting! Once assets have been inventoried, then you can start the process of `asset management`. This is a key concept in defensive security.

#### Asset Inventory

`Asset inventory` is a critical component of vulnerability management. An organization needs to understand what assets are in its network to provide the proper protection and set up appropriate defenses. The asset inventory should include information technology, operational technology, physical, software, mobile, and development assets. Organizations can utilize asset management tools to keep track of assets. The assets should have data classifications to ensure adequate security and access controls.

#### Application and System Inventory

An organization should create a thorough and complete inventory of data assets for proper asset management for defensive security. Data assets include:

- All data stored on-premises. HDDs and SSDs in endpoints (PCs and mobile devices), HDDs & SSDs in servers, external drives in the local network, optical media (DVDs, Blu-ray discs, CDs), flash media (USB sticks, SD cards). Legacy technology may include floppy disks, ZIP drives (a relic from the 1990s), and tape drives.
    
- All of the data storage that their cloud provider possesses. [Amazon Web Services](https://aws.amazon.com) (`AWS`), [Google Cloud Platform](https://cloud.google.com) (`GCP`), and [Microsoft Azure](https://azure.microsoft.com/en-us/) are some of the most popular cloud providers, but there are many more. Sometimes corporate networks are "multi-cloud," meaning they have more than one cloud provider. A company's cloud provider will provide tools that can be used to inventory all of the data stored by that particular cloud provider.
    
- All data stored within various `Software-as-a-Service (SaaS)` applications. This data is also "in the cloud" but might not all be within the scope of a corporate cloud provider account. These are often consumer services or the "business" version of those services. Think of online services such as `Google Drive`, `Dropbox`, `Microsoft Teams`, `Apple iCloud`, `Adobe Creative Suite`, `Microsoft Office 365`, `Google Docs`, and the list goes on.
    
- All of the applications a company needs to use to conduct their usual operation and business. Including applications that are deployed locally and applications that are deployed through the cloud or are otherwise Software-as-a-Service.
    
- All of a company's on-premises computer networking devices. These include but aren't limited to `routers`, `firewalls`, `hubs`, `switches`, dedicated `intrusion detection` and `prevention systems` (`IDS/IPS`), `data loss prevention` (`DLP`) systems, and so on.
    

All of these assets are very important. A threat actor or any other sort of risk to any of these assets can do significant damage to a company's information security and ability to operate day by day. An organization needs to take its time to assess everything and be careful not to miss a single data asset, or they won't be able to protect it.

Organizations frequently add or remove computers, data storage, cloud server capacity, or other data assets. Whenever data assets are added or removed, this must be thoroughly noted in the `data asset inventory`.

---

## Onwards

Next, we'll discuss some key standards that organizations may be subject to or choose to follow to standardize their approach to risk and vulnerability management.#hacking #cvss #enumeration #footprinting #vulnerability [source](https://academy.hackthebox.com/module/108/section/1228)

There are various ways to score or calculate severity ratings of vulnerabilities. The [Common Vulnerability Scoring System (CVSS)](https://www.first.org/cvss/) is an industry standard for performing these calculations. Many scanning tools will apply these scores to each finding as a part of the scan results, but it's important that we understand how these scores are derived in case we ever need to calculate one by hand or justify the score applied to a given vulnerability. The CVSS is often used together with the so-called [Microsoft DREAD](https://en.wikipedia.org/wiki/DREAD_(risk_assessment_model)). `DREAD` is a risk assessment system developed by Microsoft to help IT security professionals evaluate the severity of security threats and vulnerabilities. It is used to perform a risk analysis by using a scale of 10 points to assess the severity of security threats and vulnerabilities. With this, we calculate the risk of a threat or vulnerability based on five main factors:

- Damage Potential
- Reproducibility
- Exploitability
- Affected Users
- Discoverability

The model is essential to Microsoft's security strategy and is used to monitor, assess and respond to security threats and vulnerabilities in Microsoft products. It also serves as a reference for IT security professionals and managers to perform their risk assessment and prioritization of security threats and vulnerabilities.

---

## Risk Scoring

The CVSS system helps categorize the risk associated with an issue and allows organizations to prioritize issues based on the rating. The CVSS scoring consists of the `exploitability and impact` of an issue. The `exploitability` measurements consist of `access vector`, `access complexity`, and `authentication`. The `impact` metrics consist of the `CIA triad`, including `confidentiality`, `integrity`, and `availability`.

![metricgroup](https://academy.hackthebox.com/storage/modules/108/graphics/VulnerabilityAssessment_Diagram_08.png) _Adapted from the original graphic found [here](https://www.first.org/cvss/v3-1/media/MetricGroups.svg)._

---

## Base Metric Group

The CVSS base metric group represents the vulnerability characteristics and consists of `exploitability` metrics and `impact` metrics.

#### Exploitability Metrics

The Exploitability metrics are a way to evaluate the technical means needed to exploit the issue using the metrics below:

- Attack Vector
- Attack Complexity
- Privileges Required
- User Interaction

#### Impact Metrics

The Impact metrics represent the repercussions of successfully exploiting an issue and what is impacted in an environment, and it is based on the CIA triad. The CIA triad is an acronym for `Confidentiality`, `Integrity`, and `Availability`.

![CIA Triad](https://academy.hackthebox.com/storage/modules/108/graphics/cia_triad.png)

`Confidentiality Impact` relates to securing information and ensuring only authorized individuals have access. For example, a high severity value would be in the case of an attacker stealing passwords or encryption keys. A low severity value would relate to an attacker taking information that may not be a vital asset to an organization.

`Integrity Impact` relates to information not being changed or tampered with to maintain accuracy. For example, a high severity would be if an attacker modified crucial business files in an organization's environment. A low severity value would be if an attacker could not specifically control the number of changed or modified files.

`Availability Impact` relates to having information readily attainable for business requirements. For example, a high value would be if an attacker caused an environment to be completely unavailable for business. A low value would be if an attacker could not entirely deny access to business assets and users could still access some organization assets.

---

## Temporal Metric Group

The `Temporal Metric Group` details the availability of exploits or patches regarding the issue.

#### Exploit Code Maturity

The `Exploit Code Maturity` metric represents the probability of an issue being exploited based on ease of exploitation techniques. There are various metric values associated with this metric, including `Not Defined`, `High`, `Functional`, `Proof-of-Concept`, and `Unproven`.

A 'Not Defined' value relates to skipping this particular metric. A 'High' value represents an exploit consistently working for the issue and is easily identifiable with automated tools. A Functional value indicates there is exploit code available to the public. A Proof-of-Concept demonstrates that a PoC exploit code is available but would require changes for an attacker to exploit the issue successfully.

#### Remediation Level

The `Remediation level` is used to identify the prioritization of a vulnerability. The metric values associated with this metric include `Not Defined`, `Unavailable`, `Workaround`, `Temporary Fix`, and `Official Fix`.

A 'Not Defined' value relates to skipping this particular metric. An 'Unavailable' value indicates there is no patch available for the vulnerability. A 'Workaround' value indicates an unofficial solution released until an official patch by the vendor. A 'Temporary Fix' means an official vendor has provided a temporary solution but has not released a patch yet for the issue. An 'Official Fix' indicates a vendor has released an official patch for the issue for the public.

#### Report Confidence

`Report Confidence` represents the validation of the vulnerability and how accurate the technical details of the issue are. The metric values associated with this metric include `Not Defined`, `Confirmed`, `Reasonable`, and `Unknown`.

A 'Not Defined' value relates to skipping this particular metric. A 'Confirmed' value indicates there are various sources with detailed information confirming the vulnerability. A 'Reasonable' value indicates sources have published information about the vulnerability. However, there is no complete confidence that someone would achieve the same result due to missing details of reproducing the exploit for the issue.

---

## Environmental Metric Group

The Environmental metric group represents the significance of the vulnerability of an organization, taking into account the CIA triad.

#### Modified Base Metrics

The `Modified Base metrics` represent the metrics that can be altered if the affected organization deems a more significant risk in Confidentiality, Integrity, and Availability to their organization. The values associated with this metric are `Not Defined`, `High`, `Medium`, and `Low`.

A 'Not Defined' value would indicate skipping this metric. A 'High' value would mean one of the elements of the CIA triad would have astronomical effects on the overall organization and customers. A 'Medium' value would indicate one of the elements of the CIA triad would have significant effects on the overall organization and customers. A 'Low' value would mean one of the elements of the CIA triad would have minimal effects on the overall organization and customers.

---

## Calculating CVSS Severity

The calculation of a CVSS v3.1 score takes into account all the metrics discussed in this section. The National Vulnerability Database has a calculator available to the public [here](https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator).

#### CVSS Calculation Example

For example, for the Windows Print Spooler Remote Code Execution Vulnerability, CVSS Base Metrics is 8.8. You can reference the values of each metric value [here](https://msrc.microsoft.com/update-guide/vulnerability/CVE-2021-34527).

---

## Next Steps

Next, we'll discuss how vulnerabilities are classified in a standard way that scanning tools can use to include an external reference to the particular vulnerability.#vulnerability #hacking #scanning #enumeration #footprinting #nessus #openvas
[source](https://academy.hackthebox.com/module/108/section/1230)

As discussed earlier, vulnerability scanning is performed to identify potential vulnerabilities in network devices such as routers, firewalls, switches, as well as servers, workstations, and applications. Scanning is automated and focuses on finding potential/known vulnerabilities on the network or at the application level. `Vulnerabilities scanners typically do not exploit vulnerabilities (with some exceptions) but need a human to manually validate scan issues` to determine whether or not a particular scan returned real issues that need to be fixed or false positives that can be ignored and excluded from future scans against the same target.

Vulnerability scanning is often part of a standard penetration test, but the two are not the same. A vulnerability scan can help gain additional coverage during a penetration test or speed up the project's testing under time constraints. An actual penetration test includes much more than just a scan.

The type of scans run varies from one tool to another, but most tools `run a combination of dynamic and static tests`, depending on the target and the vulnerability. A `static test` would determine a vulnerability if the identified version of a particular asset has a public CVE. However, this is not always accurate as a patch may have been applied, or the target isn't specifically vulnerable to that CVE. On the other hand, a `dynamic test` tries specific (usually benign) payloads such as weak credentials, SQL injection, or command injection on the target (i.e., a web application). If any payload returns a hit, then there's a good chance that it is vulnerable.

Organizations should run both `unauthenticated and authenticated scans` on a continuous schedule to ensure that assets are patched as new vulnerabilities are discovered and that any new assets added to the network do not have missing patches or other configuration/patching issues. Vulnerability scanning should feed into an organization's [patch management](https://en.wikipedia.org/wiki/Patch_(computing)) program.

`Nessus`, `Nexpose`, and `Qualys` are well-known vulnerability scanning platforms that also provide free community editions. There are also open-source alternatives such as `OpenVAS`.

---

## Nessus Overview

[Nessus Essentials](https://community.tenable.com/s/article/Nessus-Essentials) by Tenable is the free version of the official Nessus Vulnerability Scanner. Individuals can access Nessus Essentials to get started understanding Tenable's vulnerability scanner. The caveat is that it can only be used for up to 16 hosts. The features in the free version are limited but are perfect for someone looking to get started with Nessus. The free scanner will attempt to identify vulnerabilities in an environment.

![image](https://academy.hackthebox.com/storage/modules/108/Nessus_Essentials___Folders___View_Scan.png)

---

## OpenVAS Overview

[OpenVAS](https://www.openvas.org/) by Greenbone Networks is a publicly available open-source vulnerability scanner. OpenVAS can perform network scans, including authenticated and unauthenticated testing.

![image](https://academy.hackthebox.com/storage/modules/108/openvas/dashboard.png)#nessus #vulnerability #scanning #footprinting #enumeration #hacking [source](https://academy.hackthebox.com/module/108/section/1231)

Let's see how we can download and set up Nessus for its first use so that we can start learning its various features. Feel free to follow along and set up a Nessus instance on your own VM. For the interactive portions of this module, we provide a lab instance of Nessus and another with OpenVAS installed.

---

## Downloading Nessus

To download Nessus, we can navigate to its [Download Page](https://www.tenable.com/downloads/nessus?loginAttempted=true) to download the correct Nessus binary for our system. We will be downloading the Debian package for `Ubuntu` for this walkthrough. ![](https://academy.hackthebox.com/storage/modules/108/openvas/deb.png)

---

## Requesting Free License

Next, we can visit the [Activation Code Page](https://www.tenable.com/products/nessus/activation-code) to request a Nessus Activation Code, which is necessary to get the free version of Nessus: ![](https://academy.hackthebox.com/storage/modules/108/nessus/register.png)

![](https://academy.hackthebox.com/storage/modules/108/nessus/registrationcode.png)

---

## Installing Package

With both the binary and activation code in hand, we can now install the Nessus package:

```shell-session
tr01ax@htb[/htb]$ dpkg -i Nessus-8.15.1-ubuntu910_amd64.deb

Selecting previously unselected package nessus.
(Reading database ... 132030 files and directories currently installed.)
Preparing to unpack Nessus-8.15.1-ubuntu910_amd64.deb ...
Unpacking nessus (8.15.1) ...
Setting up nessus (8.15.1) ...
Unpacking Nessus Scanner Core Components...
Created symlink /etc/systemd/system/nessusd.service → /lib/systemd/system/nessusd.service.
Created symlink /etc/systemd/system/multi-user.target.wants/nessusd.service → /lib/systemd/system/nessusd.service.
```

---

## Starting Nessus

Once we have Nessus installed, we can start the Nessus Service:

```shell-session
tr01ax@htb[/htb]$ sudo systemctl start nessusd.service
```

---

## Accessing Nessus

To access Nessus, we can navigate to `https://localhost:8834`. Once we arrive at the setup page, we should select `Nessus Essentials` for the free version, and then we can enter our activation code: ![](https://academy.hackthebox.com/storage/modules/108/nessus/essentials.png)

Once we enter our activation code, we can set up a user with a `secure` password for our Nessus account. Then, the plugins will begin to compile once this step is completed: ![](https://academy.hackthebox.com/storage/modules/108/nessus/init.png)

**Note:** The VM provided at the `Nessus Skills Assessment` section has Nessus pre-installed and the targets running. You can go to that section and start the VM and use Nessus throughout the module, which can be accessed at `https:// < IP >:8834`. The Nessus credentials are: `htb-student`:`HTB_@cademy_student!`. You may also use these credentials to SSH into the target VM to configure Nessus.

Finally, once the setup is complete, we can start creating scans, scan policies, plugin rules, and customizing settings. The `Settings` page has a wealth of options such as setting up a Proxy Server or SMTP server, standard account management options, and advanced settings to customize the user interface, scanning, logging, performance, and security options.

![image](https://academy.hackthebox.com/storage/modules/108/nessus/nessus_settings.png)#nessus #vulnerability #enumeration #footprinting #hacking #vulnerability #scanning 
[source](https://academy.hackthebox.com/module/108/section/1232)

We can configure a number of advanced settings for Nessus and its scans, like scan policies, plugins, and credentials, all of which we will cover in this section.

---

## Scan Policies

Nessus gives us the option to create scan policies. Essentially these are customized scans that allow us to define specific scan options, save the policy configuration, and have them available to us under `Scan Templates` when creating a new scan. This gives us the ability to create targeted scans for any number of scenarios, such as a slower, more evasive scan, a web-focused scan, or a scan for a particular client using one or several sets of credentials. Scan policies can be imported from other Nessus scanners or exported to be later imported into another Nessus scanner.

![image](https://academy.hackthebox.com/storage/modules/108/nessus/nessus_policies.png)

---

## Creating a Scan Policy

To create a scan policy, we can click on the `New Policy` button in the top right, and we will be presented with the list of pre-configured scans. We can choose a scan, such as the `Basic Network Scan`, then customize it, or we can create our own. We will choose `Advanced Scan` to create a fully customized scan with no pre-configured recommendations built-in.

After choosing the scan type as our base, we can give the scan policy a name and a description if needed: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/policy.png)

From here, we can configure settings, add in any necessary credentials, and specify any compliance standards to run the scan against. We can also choose to enable or disable entire [plugin](https://docs.tenable.com/nessus/Content/Plugins.htm) families or individual plugins.

Once we have finished customizing the scan, we can click on `Save`, and the newly created policy will appear in the polices list. From here on, when we go to create a new scan, there will be a new tab named `User Defined` under `Scan Templates` that will show all of our custom scan policies: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/htb_policydefined.png)

---

## Nessus Plugins

Nessus works with plugins written in the [Nessus Attack Scripting Language (NASL)](https://en.wikipedia.org/wiki/Nessus_Attack_Scripting_Language) and can target new vulnerabilities and CVEs. These plugins contain information such as the vulnerability name, impact, remediation, and a way to test for the presence of a particular issue.

Plugins are rated by severity level: `Critical`, `High`, `Medium`, `Low`, `Info`. At the time of this writing Tenable has published `145,973` plugins that cover `58,391` CVE IDs and `30,696` [Bugtraq](https://en.wikipedia.org/wiki/Bugtraq) IDs. A searchable database of all published plugins is on the [Tenable website](https://www.tenable.com/plugins).

The `Plugins` tab provides more information on a particular detection, including mitigation. When conducting recurring scans, there may be a vulnerability/detection that, upon further examination, is not considered to be an issue. For example, Microsoft DirectAccess (a technology that provides internal network connectivity to clients over the Internet) allows insecure and null cipher suites. The below scan performed with `sslscan` shows an example of insecure and null cipher suites:

```shell-session
tr01ax@htb[/htb]$ sslscan example.com

<SNIP>

Preferred TLSv1.0  128 bits  ECDHE-RSA-AES128-SHA          Curve 25519 DHE 253
Accepted  TLSv1.0  256 bits  ECDHE-RSA-AES256-SHA          Curve 25519 DHE 253
Accepted  TLSv1.0  128 bits  DHE-RSA-AES128-SHA            DHE 2048 bits
Accepted  TLSv1.0  256 bits  DHE-RSA-AES256-SHA            DHE 2048 bits
Accepted  TLSv1.0  128 bits  AES128-SHA                   
Accepted  TLSv1.0  256 bits  AES256-SHA                   

<SNIP>
```

However, this is by design. SSL/TLS is not [required](https://directaccess.richardhicks.com/2014/09/23/directaccess-ip-https-ssl-and-tls-insecure-cipher-suites/) in this case, and implementing it would result in a negative performance impact. To exclude this false positive from the scan results while keeping the detection active for other hosts, we can create a plugin rule: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/plugin_rules.png)

Under the `Resources` section, we can select `Plugin Rules`. In the new plugin rule, we input the host to be excluded, along with the Plugin ID for Microsoft DirectAccess, and specify the action to be performed as `Hide this result`: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/new-rule.png)

We may also want to exclude certain issues from our scan results, such as plugins for issues that are not directly exploitable (e.g., [SSL Self-Signed Certificate](https://www.tenable.com/plugins/nessus/57582)). We can do this by specifying the plugin ID and host(s) to be excluded: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/plugins2.png)

---

## Scanning with Credentials

Nessus also supports credentialed scanning and provides a lot of flexibility by supporting LM/NTLM hashes, Kerberos authentication, and password authentication.

Credentials can be configured for host-based authentication via SSH with a password, public key, certificate, or Kerberos-based authentication. It can also be configured for Windows host-based authentication with a password, Kerberos, LM hash, or NTLM hash: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/creds.png)

Nessus also supports authentication for a variety of databases types including Oracle, PostgreSQL, DB2, MySQL, SQL Server, MongoDB, and Sybase: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/db_creds.png)

**Note:** To run a credentialed scan on the target, use the following credentials: `htb-student_adm`:`HTB_@cademy_student!` for Linux, and `administrator`:`Academy_VA_adm1!` for Windows. These scans have already been set up in the Nessus target to save you time.

In addition to that, Nessus can perform plaintext authentication to services such as FTP, HTTP, IMAP, IPMI, Telnet, and more: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/plaintext_auth.png)

Finally, we can check the Nessus output to confirm whether the authentication to the target application or service with the supplied credentials was successful: ![image](https://academy.hackthebox.com/storage/modules/108/nessus/sqlserv.png)#nessus #enumeration #footprinting #scanning #vulnerability #hacking [source](https://academy.hackthebox.com/module/108/section/1024)

---

Nessus gives us the option to export scan results in a variety of report formats as well as the option to export raw Nessus scan results to be imported into other tools, archived, or passed to tools, such as [EyeWitness](https://github.com/FortyNorthSecurity/EyeWitness), which can be used to take screenshots of all web applications identified by Nessus and greatly assist us with working through the results and finding more value in them.

---

## Nessus Reports

Once a scan is completed we can choose to export a report in `.pdf`, `.html`, or `.csv` formats. The .pdf and .html reports give the option for either an Executive Summary or a custom report. The Executive Summary report provides a listing of hosts, a total number of vulnerabilities discovered per host, and a `Show Details` option to see the severity, CVSS score, plugin number, and name of each discovered issue. The plugin number contains a link to the full plugin writeup from the Tenable plugin database. The PDF option provides the scan results in a format that is easier to share. The CSV report option allows us to select which columns we would like to export. This is particularly useful if importing the scan results into another tool such as Splunk if a document needs to be shared with many internal stakeholders responsible for remediation of the various assets scanned or to perform analytics on the scan data.

![image](https://academy.hackthebox.com/storage/modules/108/nessus/exportreport.png)

**Note:** These scan reports should only be shared as either an appendix or supplementary data to a custom penetration test/vulnerability assessment report. They should not be given to a client as the final deliverable for any assessment type.

An example of the HTML report is shown below:

![image](https://academy.hackthebox.com/storage/modules/108/nessus/htmlreport.png)

It is best to always make sure the vulnerabilities are grouped together for a clear understanding of each issue and the assets affected.

---

## Exporting Nessus Scans

Nessus also gives the option to export scans into two formats `Nessus (scan.nessus)` or `Nessus DB (scan.db)`. The `.nessus` file is an `.xml` file and includes a copy of the scan settings and plugin outputs. The `.db` file contains the `.nessus` file and the scan's KB, plugin Audit Trail, and any scan attachments. More information about the `KB` and `Audit Trail` can be found [here](https://community.tenable.com/s/article/What-is-included-in-a-nessus-db-file).

Scripts such as the [nessus-report-downloader](https://raw.githubusercontent.com/eelsivart/nessus-report-downloader/master/nessus6-report-downloader.rb) can be used to quickly download scan results in all available formats from the CLI using the Nessus REST API:

```shell-session
tr01ax@htb[/htb]$ ./nessus_downloader.rb 

Nessus 6 Report Downloader 1.0

Enter the Nessus Server IP: 127.0.0.1
Enter the Nessus Server Port [8834]: 8834
Enter your Nessus Username: admin
Enter your Nessus Password (will not echo): 

Getting report list...
Scan ID Name                                               Last Modified                  Status         
------- ----                                               -------------                  ------         
1     Windows_basic                                Aug 22, 2020 22:07 +00:00      completed      
         
Enter the report(s) your want to download (comma separate list) or 'all': 1

Choose File Type(s) to Download: 
[0] Nessus (No chapter selection)
[1] HTML
[2] PDF
[3] CSV (No chapter selection)
[4] DB (No chapter selection)
Enter the file type(s) you want to download (comma separate list) or 'all': 3

Path to save reports to (without trailing slash): /assessment_data/inlanefreight/scans/nessus

Downloading report(s). Please wait...

[+] Exporting scan report, scan id: 1, type: csv
[+] Checking export status...
[+] Report ready for download...
[+] Downloading report to: /assessment_data/inlanefreight/scans/nessus/inlanefreight_basic_5y3hxp.csv

Report Download Completed!
```

We can also write our own scripts to automate many Nessus features.#nessus #hacking #vulnerability #footprinting #scanning #hacking [source](https://academy.hackthebox.com/module/108/section/1028)

Nessus is a well-known and widely used vulnerability scanning platform. However, a few best practices should be taken into consideration before starting a scan. Scans can cause issues on sensitive networks and provide false positives, no results, or have an unfavorable impact on the network. It is always best to communicate with your client (or internal stakeholders if running a scan against your own network) on whether any sensitive/legacy hosts should be excluded from the scan or if any high priority/high availability hosts should be scanned separately, outside of regular business hours, or with different scan configurations to avoid potential issues.

There are also times when a scan may return unexpected results and need to be fine-tuned.

---

## Mitigating Issues

Some firewalls will cause us to receive scan results showing either all ports open or no ports open. If this happens, a quick fix is often to configure an Advanced Scan and disable the `Ping the remote host` option. This will stop the scan from using ICMP to verify that the host is "live" and instead proceed with the scan. Some firewalls may return an "ICMP Unreachable" message that Nessus will interpret as a live host and provide many false-positive informational findings.

In sensitive networks, we can use rate-limiting to minimize impact. For example, we can adjust `Performance Options` and modify `Max Concurrent Checks Per Host` if the target host is often under heavy load, such as a widely used web application. This will limit the number of plugins used concurrently against the host.

We can avoid scanning legacy systems and choose the option not to scan printers, as we showed in an earlier section. If a host is of particular concern, it should be left out of the target scope as Nessus does not have an option for an "exclusion list" of hosts within a CIDR range like we can do with tools like Nmap.

Finally, unless specifically requested, we should never perform [Denial of Service checks](https://www.tenable.com/plugins/nessus/families/Denial%20of%20Service). We can ensure that these types of plugins are not used by always enabling the ["safe checks"](https://www.tenable.com/blog/understanding-the-nessus-safe-checks-option) option when performing scans to avoid any network plugins that can have a negative impact on a target, such as crashing a network daemon. Enabling the "safe checks" option does not guarantee that a Nessus vulnerability scan will have zero adverse impact but will significantly minimize potential impact and decrease scanning time.

It is always best to communicate with our clients or internal stakeholders and alert necessary personnel before starting a scan. When the scan is completed, we should keep detailed logs of the scanning activity in case an incident occurs that must be investigated.

---

## Network Impact

It is also essential to keep in mind the potential impact of vulnerability scanning on a network, especially on low bandwidth or congested links. This can be measured using [vnstat](https://humdi.net/vnstat/):

```shell-session
tr01ax@htb[/htb]$ sudo apt install vnstat

```

Let's monitor the `eth0` network adapter before running a Nessus scan:

```shell-session
tr01ax@htb[/htb]$ sudo vnstat -l -i eth0

Monitoring eth0...    (press CTRL-C to stop)

   rx:       332 bit/s     0 p/s          tx:       332 bit/s     0 p/s

   rx:         0 bit/s     0 p/s          tx:         0 bit/s     0 p/s
   rx:         0 bit/s     0 p/s          tx:         0 bit/s     0 p/s^C

 eth0  /  traffic statistics

                           rx         |       tx
--------------------------------------+------------------
  bytes                        572 B  |           392 B
--------------------------------------+------------------
          max              480 bit/s  |       332 bit/s
      average              114 bit/s  |        78 bit/s
          min                0 bit/s  |         0 bit/s
--------------------------------------+------------------
  packets                          8  |               5
--------------------------------------+------------------
          max                  1 p/s  |           0 p/s
      average                  0 p/s  |           0 p/s
          min                  0 p/s  |           0 p/s
--------------------------------------+------------------
  time                    40 seconds
```

We can compare this result with the result we get when monitoring the same interface during a Nessus scan against just one host:

```shell-session
tr01ax@htb[/htb]$ sudo vnstat -l -i eth0

Monitoring eth0...    (press CTRL-C to stop)

   rx:   307.92 kbit/s   641 p/s          tx:   380.41 kbit/s   767 p/s^C

 eth0  /  traffic statistics

                           rx         |       tx
--------------------------------------+------------------
  bytes                     1.04 MiB  |        1.34 MiB
--------------------------------------+------------------
          max          414.81 kbit/s  |   480.59 kbit/s
      average          230.57 kbit/s  |   296.72 kbit/s
          min                0 bit/s  |         0 bit/s
--------------------------------------+------------------
  packets                      18252  |           22733
--------------------------------------+------------------
          max                864 p/s  |         969 p/s
      average                480 p/s  |         598 p/s
          min                  0 p/s  |           0 p/s
--------------------------------------+------------------
  time                    38 seconds


real  0m38.588s
user  0m0.002s
sys 0m0.016s
```

When comparing the results, we can see that the number of bytes and packets transferred during a vulnerability scan is quite significant and can severely impact a network if not tuned properly or performed against fragile/sensitive devices.#openvas #gvm #footprinting #enumeration #scanning #vulnerability #hacking [source](https://academy.hackthebox.com/module/108/section/1026)

[OpenVAS](https://openvas.org/), by Greenbone Networks, is a publicly available vulnerability scanner. Greenbone Networks has an entire Vulnerability Manager, part of which is the OpenVAS scanner. Greenbone's Vulnerability Manager is also open to the public and free to use. OpenVAS has the capabilities to perform network scans, including authenticated and unauthenticated testing.

![image](https://academy.hackthebox.com/storage/modules/108/openvas/Greenbone_Security_Assistant.png)

We will get started with using OpenVAS by following the installation instruction below for Parrot Security. The tool is pre-installed on the host provided in a later section.

---

## Installing Package

First, we can start by installing the tool:

```shell-session
tr01ax@htb[/htb]$ sudo apt-get update && apt-get -y full-upgrade
tr01ax@htb[/htb]$ sudo apt-get install gvm && openvas
```

Next, to begin the installation process, we can run the following command below:

```shell-session
tr01ax@htb[/htb]$ gvm-setup
```

This will begin the setup process and take up to 30 minutes.

![image](https://academy.hackthebox.com/storage/modules/108/openvas/gvmsetup.png)

---

## Starting OpenVas

Finally, we can start OpenVas:

```shell-session
tr01ax@htb[/htb]$ gvm-start
```

![image](https://academy.hackthebox.com/storage/modules/108/openvas/gvmstart.png)

**Note:** The VM provided in the `OpenVAS Skills Assessment` section has OpenVAS pre-installed and the targets running. You can go to that section and start the VM and use OpenVAS throughout the module, which can be accessed at `https://< IP >:8080`. The OpenVAS credentials are: `htb-student`:`HTB_@cademy_student!`. You may also use these credentials to SSH into the target VM to configure OpenVAS.#openvas #gvm #scanning #vulnerability #enumeration #footprinting #hacking [source](https://academy.hackthebox.com/module/108/section/1463)

The OpenVAS Greenbone Security Assistant application has various tabs that you can interact with. For this section, we will be digging into the scans. If you navigate to the `Scans` tab shown below, you will see the scans that have run in the past. You will also be able to see how to create a new task to run a scan. The tasks work off of the scanning configurations that the user sets up.

**Note:** The scans shown in this section have already been pre-run to save you the time of waiting for them to finish. If you re-run the scan, it's best to go through vulnerabilities as they come, instead of waiting for the scan to finish, as they can take 1-2 hours to finish.

![Scans](https://academy.hackthebox.com/storage/modules/108/openvas/creatingscan1.png)

**Note:** For this module, the Windows target will be `172.16.16.100` and the Linux target will be `*************`.

![Scansconfigs](https://academy.hackthebox.com/storage/modules/108/openvas/scanconfigs.png)

---

## Configuration

Before setting up any scans, it is best to configure the targets for the scan. If you navigate to the `Configurations` tab and select `Targets`, you will see targets that have been already added to the application.

![targetstab](https://academy.hackthebox.com/storage/modules/108/openvas/targets.png)

To add your own, click the icon highlighted below and add an individual target or a host list. You also can configure other options such as the ports, authentication, and methods of identifying if the host is reachable. For the `Alive Test`, the `Scan Config Default` option from OpenVAS leverages the `NVT Ping Host` in the `NVT Family`. You can learn about the NVT Family [here](https://docs.greenbone.net/GSM-Manual/gos-6/en/scanning.html#vulnerabilitymanagement-create-target).

![createtarget](https://academy.hackthebox.com/storage/modules/108/openvas/addingtarget.png)

Typically, an `authenticated scan` leverages a high privileged user such as `root` or `Administrator`. Depending on the permission level for the user, if it's the highest permission level, you'll retrieve the maximum amount of information back from the host in regards to the vulnerabilities present since you would have full access.

**Note:** To run a credentialed scan on the target, use the following credentials: `htb-student_adm`:`HTB_@cademy_student!` for Linux, and `administrator`:`Academy_VA_adm1!` for Windows. These scans have already been set up in the OpenVAS target to save you time.

Once you have added your target, they will appear in the list below: ![targetsview](https://academy.hackthebox.com/storage/modules/108/openvas/targetsview.png)

---

## Setting Up a Scan

Multiple scan configurations leverage OpenVAS Network Vulnerability Test (NVT) Families, which consist of many different categories of vulnerabilities, such as ones for Windows, Linux, Web Applications, etc. You can see a few different types of families shown below: ![nvt](https://academy.hackthebox.com/storage/modules/108/openvas/nvt2.png)

OpenVAS has various scan configurations to choose from for scanning a network. We recommend only leveraging the ones below, as other options could cause system disruptions on a network:

- `Base`: This scan configuration is meant to enumerate information about the host's status and operating system information. This scan configuration does not check for vulnerabilities.
    
- `Discovery`: This scan configuration is meant to enumerate information about the system. The configuration identifies the host's services, hardware, accessible ports, and software being used on the system. This scan configuration also does not check for vulnerabilities.
    
- `Host Discovery`: This scan configuration solely tests whether the host is alive and determines what devices are `active` on the network. This scan configuration does not check for vulnerabilities as well. _OpenVAS leverages ping to identify if the host is alive._
    
- `System Discovery`: This scan enumerates the target host further than the 'Discovery Scan' and attempts to identify the operating system and hardware associated with the host.
    
- `Full and fast`: This configuration is recommended by OpenVAS as the safest option and leverages intelligence to use the best NVT checks for the host(s) based on the accessible ports.
    

You can create your own scan by navigating to the 'Scans' tab and clicking the wizard icon. ![Scans2](https://academy.hackthebox.com/storage/modules/108/openvas/creatingscan2.png)

Once you click the wizard icon, the panel shown below will pop up and allow you to configure your scan.

![CreateScan](https://academy.hackthebox.com/storage/modules/108/openvas/Newscan.png)

We will configure the scan with the options below, which targets `*************` and then run our scan, which can take `30-60 minutes` to finish.

![linux_basic](https://academy.hackthebox.com/storage/modules/108/openvas/linux_basic.png)

![linux_target_unauth](https://academy.hackthebox.com/storage/modules/108/openvas/linux_unauthedtarget.png)#openvas #gvm #scanning #footprinting #enumeration #hacking #vulnerability [source](https://academy.hackthebox.com/module/108/section/1495)

OpenVAS provides the scan results in a report that can be accessed when you are on the `Scans` page, as shown below.

![viewreport](https://academy.hackthebox.com/storage/modules/108/openvas/viewingreport.png)

Once you click the report, you can view the scan results and operating system information, open ports, services, etc., in other tabs in the scan report.

![results](https://academy.hackthebox.com/storage/modules/108/openvas/openvas_reports.png)

---

## Exporting Formats

There are various export formats for reporting purposes, including XML, CSV, PDF, ITG, and TXT. If you choose to export your report out as an XML, you can leverage various XML parsers to view the data in an easier to read format.

![openvas_reportformats](https://academy.hackthebox.com/storage/modules/108/openvas/reportformat.png)

We will export our results in XML and use the [openvasreporting](https://github.com/TheGroundZero/openvasreporting) tool by the TheGroundZero. The `openvasreporting` tool offers various options when generating output. We are using the standard option for an Excel file for this report.

```shell-session
tr01ax@htb[/htb]$ python3 -m openvasreporting -i report-2bf466b5-627d-4659-bea6-1758b43235b1.xml -f xlsx
```

This command will generate an excel document similar to the one below:

![openvas_reportexcel](https://academy.hackthebox.com/storage/modules/108/openvas/openvas_report.png)

![report_toc](https://academy.hackthebox.com/storage/modules/108/openvas/report_toc.png)#reporting #openvas #nessus #vulnerability #scanning #hacking 

---

Soft skills in information security are critical to being successful in your role. Although vulnerability scanning tools leverage automated tools, there is still a need to transfer the information to a client-ready report. The report should be readable by anyone ranging from a technical person to a non-technical person. A strong report consists of the following sections:

- Executive Summary
- Overview of Assessment
- Scope
- Vulnerabilities and Recommendations

---

## Executive Summary

The `Executive Summary` of a vulnerability assessment report is intended to be readable by an executive who needs a high-level overview of the details and what is the most important items to fix immediately, depending on the severity. This section allows an executive to look at the report and prioritize remediations based on the summary.

You can also include a graphical view of the number of vulnerabilities based on the severity here, similar to the graph below: ![graph](https://academy.hackthebox.com/storage/modules/108/graph.png)

---

## Overview of Assessment

The `Overview of the Assessment` should include any methodology leveraged during the assessment. The methodology should detail the execution of the assessment during the testing period, such as discussing the process and tools used for the project (e.g., Nessus).

---

## Scope and Duration

The `Scope and Duration` section of the report should include everything the client authorized for the assessment, including the target scope and the testing period.

---

## Vulnerabilities and Recommendations

The `Vulnerabilities and Recommendations` section should detail the findings discovered during the vulnerability assessment once you've eliminated any false positives by manually testing them. It is best to group findings that relate to each other based on the type of issues or their severity.

Each issue should have the following elements:

- Vulnerability Name
- CVE
- CVSS
- Description of Issue
- References
- Remediation Steps
- Proof of Concept
- Affected Systems

---

## Closing

The reporting portion of any assessment is the most crucial part of the project. Always make sure you are writing your reports such that any audience can read them. When discussing technical information, always reference what you describe for the reader to understand or reproduce what you are talking about in the report. Additionally, sentences should be to the point with proper grammar as well. The strongest reports are concise and clear for a reader.also see [[Lesson 01 - Security Assessment]]

![[Pasted image 20231027133704.png]]


![[Pasted image 20231027133555.png]]

