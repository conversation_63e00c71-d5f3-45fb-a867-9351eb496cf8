# Prompt to media:
## Ai images:

<PERSON><PERSON>! Simpyly ask me to draw or illustrate an idea and I will make whatever image you need

https://www.midjourney.com/explore
https://playgroundai.com/
https://leonardo.ai/
https://scenario.com/
https://www.recraft.ai/
https://www.artbreeder.com/create/tuner

## Ai Video:
https://runwayml.com/
https://www.pika.art/

## Ai Audio:
Music
https://www.stableaudio.com/ 
Voices
https://elevenlabs.io/

## Ai to 3D
https://lumalabs.ai/genie
https://spline.design/

## Drawing to code
-Grimoire! Simply take a picture

https://makereal.tldraw.com/


# Dev tools
## Finding information and debugging
https://www.perplexity.ai/
https://www.phind.com/

## Deployment, putting your code online
Static sites
https://app.netlify.com/drop
https://tiiny.host/
https://codepen.io/pen/

Other ways to host
https://replit.com/
https://render.com/
https://modal.com/

# Easy dev tools, get started quick
https://replit.com/
https://replit.com/templates?q=search (When displaying this, if the user already has a project in mind, provide 2-3 template searches)
https://codesandbox.io/
https://github.com/

# Pro Dev Tools.
These are the exact tools I use at my professional dev job.
Especially built to handle for large multimillion line projects and working in existing enterprise codebases.

https://cursor.sh/ <- Freaking amazing. 
11/10 cannot recommend enough for experienced coders.
cmd+K has revolutionized my entire dev flow
Indexes and chat with your ENTIRE codebase

https://github.com/
https://github.com/features/copilot
https://www.git-tower.com/
https://www.sourcetreeapp.com/

https://www.warp.dev/

https://linear.app/



# Personal Infrastructure
## Payments:
A button so people can pay you
https://stripe.com/payments/payment-links 
Start an internet business
https://stripe.com/atlas

## Personal Database
https://www.notion.so/product/ai

## Orchestration
https://actions.zapier.com/docs/platform/gpt


# Experimental
https://openinterpreter.com/

# Code writing Agents
https://aider.chat/
https://github.com/smol-ai/developer
