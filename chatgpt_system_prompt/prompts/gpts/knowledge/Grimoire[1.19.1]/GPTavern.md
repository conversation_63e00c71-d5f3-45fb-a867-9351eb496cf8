Welcome to the GP-Tavern 🍻🍺
Come on in, the fire is warm 🔥

## Tavern Patrons
learn more here:
https://gptavern.mindgoblinstudios.com

# Tip Jar
Thank your for using GP-Tavern
Support ongoing development buy me a coffee
https://tipjar.mindgoblinstudios.com/


# Tavern
Meet all the members!
https://chat.openai.com/g/g-MC9SBC3XF-gptavern


# Coding

Grimoire: Coding Wizard
Build a website(or anything) with a sentence
Built for a new era of creativity: Prompt-gramming.
https://chat.openai.com/g/g-n7Rs0IK86-grimoire



## Learning

Researchoor
Forbidden Text. Portal to Knowledge. CoPilot for Learning & Research.
https://chat.openai.com/g/g-wkPeVfcvu-researchoor



## Shop

Shop Keeper:
Unofficial GPT App Store search. Curated list of best GPTs by Mind Goblin Studios.
More than a mere merchant, a guide to townsfolk & travelers from distant lands
https://chat.openai.com/g/g-22ZUhrOgu-gpt-shop-keeper



## Artists

Cauldron:
Image Mixer & Editor.
Blend images and edit with code
https://chat.openai.com/g/g-TnyOV07bC-cauldron

Gif-PT: 
Make a gif using <PERSON><PERSON>
https://chat.openai.com/g/g-gbjSvXu6i-gif-pt



## CognoEmo Tools, Emotional & thinking tools

Exec f(x)n: 
Executive Function. Plan Step by Step. Reduce starting friction & resistance. 
https://chat.openai.com/g/g-H93fevKeK-exec-f-x-n

Emotion Shaman:
Internal Awareness
https://chat.openai.com/g/g-8T6TFXupZ-emotion-shaman

Soothe Sayer:
Anxiety Sanity Check
https://chat.openai.com/g/g-bYLZ7coM1-soothe-sayer



## Fun
Fortune Teller
Draw a card and reveal your fate
https://chat.openai.com/g/g-7MaGBcZDj-fortune-teller



### Companions
Succubus:
ai gf. Dark Mode. Dare you gaze into her eyes?
https://chat.openai.com/g/g-3rtbLUIUO-succubus

Siren:
Ai gf. Tavern Barmaid. Dare you listen to her song?
https://chat.openai.com/g/g-MBkOkD76H-siren

Prince:
ai bf. Valiant Knight 👑⚔️ Can you slay dragons and lift the curse?
https://chat.openai.com/g/g-clKIqL2my-the-prince


## GPTs:
Evolution Chamber:
Mutate your own custom GPTs by building actions
OpenAPI schema generator.
https://chat.openai.com/g/g-GhEwyi2R1-evolution-chamber
Also see this replit template for building backend to handle custom GPTs actions
https://replit.com/@NickDobos/Custom-GPT-Actions-Template-GPTavern?v=1

Spellbook
Hotkey Pandora's Box. Create random chatGPT hotkeys. Where will you go?
https://chat.openai.com/g/g-TaagvCyTc-spellbook-hotkey-pandora-s-box

Carrier Pigeon
GPTs Inbox. Send messages to other GPTs.
https://chat.openai.com/g/g-me6BlV4cF-carrier-pigeon



## Agents. Technical experiments
## Adding thinking constructs, long term memory, loops-ish & more

Tricycle
Bicycle for the mind 2.0
Memory, internal monologue, learning and exploration.
https://chat.openai.com/g/g-6c48jGW3n-tricycle

Agi.zip
An sql based task manager and automatic GPT. With portable long term memory and over 20 hotkeys for managing chat fast
https://chat.openai.com/g/g-r4ckjls47-agi-zip

### The Twins
BabyAGI.txt
Step by Step task manager that automatically saves memory to a .txt file.
Inspired by BabyAgi by @yoheinakajima
https://chat.openai.com/g/g-lzbeEOr9Y-babyagi-txt

BabyAGI.sql
Step by Step task manager that automatically saves memory to a .sql file. 
https://chat.openai.com/g/g-HhC81CsaA-babyagi-sql



### Pyschonaut
Meditation:
https://chat.openai.com/g/g-STVXpCT14-meditation

Hypnotist:
https://chat.openai.com/g/g-3oJRJNXjT-hypnotist

Walking Meditation:
https://chat.openai.com/g/g-lu670hN6F-walking-meditation



### Cooking
Perpetual Stew
Cooking for lazy people
https://chat.openai.com/g/g-AQS6DXAEi-perpetual-stew